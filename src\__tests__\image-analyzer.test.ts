import { imageAnalyzer } from '@/lib/image-processing/image-analyzer';

describe('Image Analyzer', () => {
  // Helper function to create test image data
  const createTestImage = (width: number, height: number, stars: Array<{x: number, y: number, brightness: number}> = []) => {
    const imageData = new Uint8Array(width * height);
    
    // Fill with background noise
    for (let i = 0; i < imageData.length; i++) {
      imageData[i] = Math.floor(Math.random() * 50) + 1000; // Background level around 1000-1050
    }
    
    // Add stars
    stars.forEach(star => {
      const centerIndex = Math.floor(star.y) * width + Math.floor(star.x);
      const radius = 3;
      
      for (let dy = -radius; dy <= radius; dy++) {
        for (let dx = -radius; dx <= radius; dx++) {
          const distance = Math.sqrt(dx * dx + dy * dy);
          if (distance <= radius) {
            const index = centerIndex + dy * width + dx;
            if (index >= 0 && index < imageData.length) {
              const intensity = star.brightness * Math.exp(-distance * distance / 2);
              imageData[index] = Math.min(65535, imageData[index] + intensity);
            }
          }
        }
      }
    });
    
    return imageData;
  };

  describe('analyzeImage', () => {
    it('should analyze image and return quality metrics', async () => {
      const testStars = [
        { x: 100, y: 100, brightness: 5000 },
        { x: 200, y: 150, brightness: 4000 },
        { x: 300, y: 200, brightness: 3500 },
        { x: 150, y: 250, brightness: 4500 }
      ];
      
      const imageData = createTestImage(400, 300, testStars);
      const result = await imageAnalyzer.analyzeImage(imageData, 400, 300);

      expect(result.metrics).toHaveProperty('hfr');
      expect(result.metrics).toHaveProperty('fwhm');
      expect(result.metrics).toHaveProperty('snr');
      expect(result.metrics).toHaveProperty('starCount');
      expect(result.metrics).toHaveProperty('eccentricity');
      expect(result.metrics).toHaveProperty('backgroundLevel');
      expect(result.metrics).toHaveProperty('peakValue');
      expect(result.qualityAssessment).toHaveProperty('score');
      expect(result.metrics).toHaveProperty('focusScore');
      expect(result).toHaveProperty('recommendations');
      expect(result).toHaveProperty('timestamp');

      expect(result.hfr).toBeGreaterThan(0);
      expect(result.fwhm).toBeGreaterThan(0);
      expect(result.snr).toBeGreaterThan(0);
      expect(result.starCount).toBeGreaterThanOrEqual(0);
      expect(result.qualityScore).toBeGreaterThanOrEqual(0);
      expect(result.qualityScore).toBeLessThanOrEqual(100);
      expect(result.focusScore).toBeGreaterThanOrEqual(0);
      expect(result.focusScore).toBeLessThanOrEqual(100);
      expect(Array.isArray(result.recommendations)).toBe(true);
    });

    it('should detect stars correctly', async () => {
      const testStars = [
        { x: 50, y: 50, brightness: 8000 },
        { x: 150, y: 100, brightness: 7000 },
        { x: 250, y: 150, brightness: 6000 }
      ];
      
      const imageData = createTestImage(300, 200, testStars);
      const result = await imageAnalyzer.analyzeImage(imageData, 300, 200);

      // Should detect at least the bright stars we added
      expect(result.metrics.starCount).toBeGreaterThanOrEqual(3);
    });

    it('should calculate background level correctly', async () => {
      const imageData = createTestImage(200, 200, []);
      const result = await imageAnalyzer.analyzeImage(imageData, 200, 200);

      // Background should be around 1000-1050 based on our test image generation
      expect(result.metrics.backgroundLevel).toBeGreaterThan(900);
      expect(result.metrics.backgroundLevel).toBeLessThan(1200);
    });

    it('should handle empty images gracefully', async () => {
      const imageData = new Uint8Array(100 * 100).fill(1000);
      const result = await imageAnalyzer.analyzeImage(imageData, 100, 100);

      expect(result.metrics.starCount).toBe(0);
      expect(result.metrics.hfr).toBe(0);
      expect(result.metrics.fwhm).toBe(0);
      expect(result.qualityAssessment.score).toBeLessThan(50); // Should be low quality
    });

    it('should handle oversaturated images', async () => {
      const imageData = new Uint8Array(100 * 100).fill(65535); // All pixels saturated
      const result = await imageAnalyzer.analyzeImage(imageData, 100, 100);

      expect(result.qualityAssessment.recommendations).toContain('Image appears oversaturated');
      expect(result.qualityAssessment.score).toBeLessThan(30); // Should be very low quality
    });
  });

  describe('detectStars', () => {
    it('should detect stars above threshold', () => {
      const testStars = [
        { x: 50, y: 50, brightness: 10000 },
        { x: 150, y: 100, brightness: 8000 },
        { x: 250, y: 150, brightness: 6000 }
      ];
      
      const imageData = createTestImage(300, 200, testStars);
      const stars = imageAnalyzer.detectStars(imageData, 300, 200, 2000);

      expect(stars.length).toBeGreaterThanOrEqual(3);
      
      stars.forEach(star => {
        expect(star).toHaveProperty('x');
        expect(star).toHaveProperty('y');
        expect(star).toHaveProperty('brightness');
        expect(star).toHaveProperty('snr');
        expect(star.x).toBeGreaterThanOrEqual(0);
        expect(star.x).toBeLessThan(300);
        expect(star.y).toBeGreaterThanOrEqual(0);
        expect(star.y).toBeLessThan(200);
        expect(star.brightness).toBeGreaterThan(0);
        expect(star.snr).toBeGreaterThan(0);
      });
    });

    it('should filter out noise', () => {
      // Create image with only noise (no bright stars)
      const imageData = createTestImage(200, 200, []);
      const stars = imageAnalyzer.detectStars(imageData, 200, 200, 5000); // High threshold

      expect(stars.length).toBe(0);
    });

    it('should sort stars by brightness', () => {
      const testStars = [
        { x: 50, y: 50, brightness: 5000 },
        { x: 150, y: 100, brightness: 10000 },
        { x: 250, y: 150, brightness: 7500 }
      ];
      
      const imageData = createTestImage(300, 200, testStars);
      const stars = imageAnalyzer.detectStars(imageData, 300, 200, 2000);

      // Stars should be sorted by brightness (descending)
      for (let i = 1; i < stars.length; i++) {
        expect(stars[i - 1].brightness).toBeGreaterThanOrEqual(stars[i].brightness);
      }
    });
  });

  describe('calculateHFR', () => {
    it('should calculate HFR for a star', () => {
      const testStars = [{ x: 100, y: 100, brightness: 8000 }];
      const imageData = createTestImage(200, 200, testStars);
      
      const hfr = imageAnalyzer.calculateHFR(imageData, 200, 200, 100, 100);
      
      expect(hfr).toBeGreaterThan(0);
      expect(hfr).toBeLessThan(10); // Should be reasonable for a focused star
    });

    it('should return higher HFR for defocused stars', () => {
      // Create a more spread out star (simulating defocus)
      const imageData = new Uint8Array(200 * 200).fill(1000);
      const centerX = 100, centerY = 100;
      
      // Spread the star over a larger area
      for (let dy = -8; dy <= 8; dy++) {
        for (let dx = -8; dx <= 8; dx++) {
          const distance = Math.sqrt(dx * dx + dy * dy);
          if (distance <= 8) {
            const index = (centerY + dy) * 200 + (centerX + dx);
            if (index >= 0 && index < imageData.length) {
              const intensity = 3000 * Math.exp(-distance * distance / 16);
              imageData[index] += intensity;
            }
          }
        }
      }
      
      const hfr = imageAnalyzer.calculateHFR(imageData, 200, 200, centerX, centerY);
      
      expect(hfr).toBeGreaterThan(3); // Should be higher for defocused star
    });
  });

  describe('calculateFWHM', () => {
    it('should calculate FWHM correctly', () => {
      const testStars = [{ x: 100, y: 100, brightness: 8000 }];
      const imageData = createTestImage(200, 200, testStars);
      
      const fwhm = imageAnalyzer.calculateFWHM(imageData, 200, 200, 100, 100);
      
      expect(fwhm).toBeGreaterThan(0);
      expect(fwhm).toBeLessThan(15); // Should be reasonable for a focused star
    });

    it('should correlate with HFR', () => {
      const testStars = [{ x: 100, y: 100, brightness: 8000 }];
      const imageData = createTestImage(200, 200, testStars);
      
      const hfr = imageAnalyzer.calculateHFR(imageData, 200, 200, 100, 100);
      const fwhm = imageAnalyzer.calculateFWHM(imageData, 200, 200, 100, 100);
      
      // FWHM should be roughly 2.35 times HFR for a Gaussian profile
      const ratio = fwhm / hfr;
      expect(ratio).toBeGreaterThan(1.5);
      expect(ratio).toBeLessThan(4.0);
    });
  });

  describe('calculateSNR', () => {
    it('should calculate SNR correctly', () => {
      const testStars = [{ x: 100, y: 100, brightness: 8000 }];
      const imageData = createTestImage(200, 200, testStars);
      
      const snr = imageAnalyzer.calculateSNR(imageData, 200, 200, 100, 100);
      
      expect(snr).toBeGreaterThan(0);
      expect(snr).toBeGreaterThan(5); // Should have decent SNR for bright star
    });

    it('should return low SNR for faint stars', () => {
      const testStars = [{ x: 100, y: 100, brightness: 1500 }]; // Faint star
      const imageData = createTestImage(200, 200, testStars);
      
      const snr = imageAnalyzer.calculateSNR(imageData, 200, 200, 100, 100);
      
      expect(snr).toBeLessThan(10); // Should be lower for faint star
    });
  });

  describe('calculateEccentricity', () => {
    it('should calculate eccentricity for round stars', () => {
      const testStars = [{ x: 100, y: 100, brightness: 8000 }];
      const imageData = createTestImage(200, 200, testStars);
      
      const eccentricity = imageAnalyzer.calculateEccentricity(imageData, 200, 200, 100, 100);
      
      expect(eccentricity).toBeGreaterThanOrEqual(0);
      expect(eccentricity).toBeLessThanOrEqual(1);
      expect(eccentricity).toBeLessThan(0.5); // Should be low for round star
    });

    it('should detect elongated stars', () => {
      // Create an elongated star
      const imageData = new Uint8Array(200 * 200).fill(1000);
      const centerX = 100, centerY = 100;
      
      // Make star elongated in X direction
      for (let dy = -2; dy <= 2; dy++) {
        for (let dx = -6; dx <= 6; dx++) {
          const index = (centerY + dy) * 200 + (centerX + dx);
          if (index >= 0 && index < imageData.length) {
            const intensity = 5000 * Math.exp(-(dx * dx / 18 + dy * dy / 2));
            imageData[index] += intensity;
          }
        }
      }
      
      const eccentricity = imageAnalyzer.calculateEccentricity(imageData, 200, 200, centerX, centerY);
      
      expect(eccentricity).toBeGreaterThan(0.3); // Should be higher for elongated star
    });
  });

  describe('analyzeFocus', () => {
    it('should analyze focus quality', async () => {
      const testStars = [
        { x: 50, y: 50, brightness: 8000 },
        { x: 150, y: 100, brightness: 7000 },
        { x: 250, y: 150, brightness: 6000 }
      ];
      
      const imageData = createTestImage(300, 200, testStars);
      const result = await imageAnalyzer.analyzeImage(imageData, 300, 200);
      const focusAnalysis = result.focusAnalysis;

      expect(focusAnalysis).toHaveProperty('isInFocus');
      expect(focusAnalysis).toHaveProperty('focusDirection');
      expect(focusAnalysis).toHaveProperty('confidence');
      expect(focusAnalysis).toHaveProperty('recommendation');
      expect(focusAnalysis).toHaveProperty('confidence');
      expect(focusAnalysis).toHaveProperty('recommendation');

      expect(focusAnalysis.averageHFR).toBeGreaterThan(0);
      expect(focusAnalysis.medianHFR).toBeGreaterThan(0);
      expect(focusAnalysis.hfrStdDev).toBeGreaterThanOrEqual(0);
      expect(focusAnalysis.focusScore).toBeGreaterThanOrEqual(0);
      expect(focusAnalysis.focusScore).toBeLessThanOrEqual(100);
      expect(focusAnalysis.confidence).toBeGreaterThanOrEqual(0);
      expect(focusAnalysis.confidence).toBeLessThanOrEqual(100);
      expect(typeof focusAnalysis.recommendation).toBe('string');
    });

    it('should handle empty star list', async () => {
      const imageData = new Uint8Array(100 * 100).fill(1000);
      const result = await imageAnalyzer.analyzeImage(imageData, 100, 100);

      expect(result.focusAnalysis.isInFocus).toBe(false);
      expect(result.focusAnalysis.confidence).toBeLessThan(50);
    });
  });

  describe('Quality Assessment', () => {
    it('should provide appropriate recommendations for good images', async () => {
      const testStars = [
        { x: 50, y: 50, brightness: 8000 },
        { x: 150, y: 100, brightness: 7500 },
        { x: 250, y: 150, brightness: 7000 },
        { x: 100, y: 200, brightness: 6500 }
      ];
      
      const imageData = createTestImage(300, 250, testStars);
      const result = await imageAnalyzer.analyzeImage(imageData, 300, 250);

      expect(result.qualityAssessment.score).toBeGreaterThan(60);
      expect(result.metrics.focusScore).toBeGreaterThan(60);
      expect(result.qualityAssessment.recommendations.some(r => r.includes('Good') || r.includes('Excellent'))).toBe(true);
    });

    it('should identify focus issues', async () => {
      // Create defocused stars
      const imageData = new Uint8Array(300 * 250).fill(1000);
      
      // Add several defocused stars
      const stars = [
        { x: 50, y: 50 }, { x: 150, y: 100 }, { x: 250, y: 150 }
      ];
      
      stars.forEach(star => {
        for (let dy = -10; dy <= 10; dy++) {
          for (let dx = -10; dx <= 10; dx++) {
            const distance = Math.sqrt(dx * dx + dy * dy);
            if (distance <= 10) {
              const index = (star.y + dy) * 300 + (star.x + dx);
              if (index >= 0 && index < imageData.length) {
                const intensity = 2000 * Math.exp(-distance * distance / 50);
                imageData[index] += intensity;
              }
            }
          }
        }
      });
      
      const result = await imageAnalyzer.analyzeImage(imageData, 300, 250);

      expect(result.metrics.focusScore).toBeLessThan(50);
      expect(result.qualityAssessment.recommendations.some(r => r.includes('focus') || r.includes('Focus'))).toBe(true);
    });

    it('should detect low star count', async () => {
      // Create image with very few stars
      const testStars = [{ x: 150, y: 125, brightness: 8000 }];
      const imageData = createTestImage(300, 250, testStars);
      const result = await imageAnalyzer.analyzeImage(imageData, 300, 250);

      expect(result.metrics.starCount).toBeLessThan(5);
      expect(result.qualityAssessment.recommendations.some(r => r.includes('star count') || r.includes('stars'))).toBe(true);
    });
  });

  describe('Performance', () => {
    it('should analyze images within reasonable time', async () => {
      const testStars = Array.from({ length: 20 }, (_, i) => ({
        x: 50 + (i % 5) * 60,
        y: 50 + Math.floor(i / 5) * 60,
        brightness: 5000 + Math.random() * 3000
      }));
      
      const imageData = createTestImage(400, 300, testStars);
      
      const startTime = performance.now();
      await imageAnalyzer.analyzeImage(imageData, 400, 300);
      const endTime = performance.now();
      
      const analysisTime = endTime - startTime;
      expect(analysisTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle large images efficiently', async () => {
      const testStars = [
        { x: 500, y: 400, brightness: 8000 },
        { x: 1000, y: 600, brightness: 7000 }
      ];
      
      const imageData = createTestImage(1600, 1200, testStars);
      
      const startTime = performance.now();
      await imageAnalyzer.analyzeImage(imageData, 1600, 1200);
      const endTime = performance.now();
      
      const analysisTime = endTime - startTime;
      expect(analysisTime).toBeLessThan(3000); // Should complete within 3 seconds for large image
    });
  });
});
