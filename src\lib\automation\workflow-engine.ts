import { Target } from '../target-planning/target-database';
import { EquipmentProfile } from '../stores/equipment-store';
import { WeatherConditions, AstronomicalConditions } from '../weather/weather-service';

export interface WorkflowNode {
  id: string;
  type: WorkflowNodeType;
  name: string;
  description?: string;
  position: { x: number; y: number };
  inputs: WorkflowInput[];
  outputs: WorkflowOutput[];
  config: Record<string, any>;
  enabled: boolean;
  executionTime?: number; // milliseconds
  lastExecuted?: Date;
  status: 'idle' | 'running' | 'completed' | 'failed' | 'skipped';
  error?: string;
}

export interface WorkflowConnection {
  id: string;
  sourceNodeId: string;
  sourceOutputId: string;
  targetNodeId: string;
  targetInputId: string;
  condition?: WorkflowCondition;
}

export interface WorkflowInput {
  id: string;
  name: string;
  type: WorkflowDataType;
  required: boolean;
  defaultValue?: any;
  description?: string;
}

export interface WorkflowOutput {
  id: string;
  name: string;
  type: WorkflowDataType;
  description?: string;
}

export interface WorkflowCondition {
  type: 'always' | 'success' | 'failure' | 'custom';
  expression?: string; // JavaScript expression for custom conditions
}

export type WorkflowNodeType = 
  | 'trigger'
  | 'equipment'
  | 'imaging'
  | 'processing'
  | 'condition'
  | 'action'
  | 'notification'
  | 'delay'
  | 'loop'
  | 'script';

export type WorkflowDataType = 
  | 'string'
  | 'number'
  | 'boolean'
  | 'target'
  | 'image'
  | 'equipment'
  | 'weather'
  | 'coordinates'
  | 'array'
  | 'object';

export interface Workflow {
  id: string;
  name: string;
  description?: string;
  version: string;
  author: string;
  created: Date;
  modified: Date;
  tags: string[];
  nodes: WorkflowNode[];
  connections: WorkflowConnection[];
  variables: WorkflowVariable[];
  triggers: WorkflowTrigger[];
  enabled: boolean;
  executionCount: number;
  lastExecution?: Date;
  averageExecutionTime?: number;
}

export interface WorkflowVariable {
  id: string;
  name: string;
  type: WorkflowDataType;
  value: any;
  description?: string;
  scope: 'global' | 'workflow' | 'node';
}

export interface WorkflowTrigger {
  id: string;
  type: 'manual' | 'scheduled' | 'event' | 'condition';
  config: Record<string, any>;
  enabled: boolean;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  triggeredBy: string;
  nodeExecutions: NodeExecution[];
  variables: Record<string, any>;
  logs: WorkflowLog[];
  error?: string;
}

export interface NodeExecution {
  nodeId: string;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'completed' | 'failed' | 'skipped';
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  error?: string;
  logs: string[];
}

export interface WorkflowLog {
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error';
  nodeId?: string;
  message: string;
  data?: any;
}

export class WorkflowEngine {
  private workflows: Map<string, Workflow> = new Map();
  private executions: Map<string, WorkflowExecution> = new Map();
  private nodeHandlers: Map<WorkflowNodeType, NodeHandler> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();

  constructor() {
    this.initializeNodeHandlers();
  }

  // Workflow Management
  createWorkflow(workflow: Omit<Workflow, 'id' | 'created' | 'modified' | 'executionCount'>): Workflow {
    const newWorkflow: Workflow = {
      ...workflow,
      id: this.generateId(),
      created: new Date(),
      modified: new Date(),
      executionCount: 0
    };

    this.workflows.set(newWorkflow.id, newWorkflow);
    this.emit('workflow:created', newWorkflow);
    return newWorkflow;
  }

  updateWorkflow(workflowId: string, updates: Partial<Workflow>): Workflow | null {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) return null;

    const updatedWorkflow = {
      ...workflow,
      ...updates,
      modified: new Date()
    };

    this.workflows.set(workflowId, updatedWorkflow);
    this.emit('workflow:updated', updatedWorkflow);
    return updatedWorkflow;
  }

  deleteWorkflow(workflowId: string): boolean {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) return false;

    this.workflows.delete(workflowId);
    this.emit('workflow:deleted', workflow);
    return true;
  }

  getWorkflow(workflowId: string): Workflow | null {
    return this.workflows.get(workflowId) || null;
  }

  getAllWorkflows(): Workflow[] {
    return Array.from(this.workflows.values());
  }

  // Workflow Execution
  async executeWorkflow(
    workflowId: string, 
    triggeredBy: string = 'manual',
    initialVariables: Record<string, any> = {}
  ): Promise<WorkflowExecution> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    if (!workflow.enabled) {
      throw new Error(`Workflow ${workflowId} is disabled`);
    }

    const execution: WorkflowExecution = {
      id: this.generateId(),
      workflowId,
      startTime: new Date(),
      status: 'running',
      triggeredBy,
      nodeExecutions: [],
      variables: { ...initialVariables },
      logs: []
    };

    this.executions.set(execution.id, execution);
    this.emit('execution:started', execution);

    try {
      await this.executeWorkflowNodes(workflow, execution);
      
      execution.status = 'completed';
      execution.endTime = new Date();
      
      // Update workflow statistics
      workflow.executionCount++;
      workflow.lastExecution = execution.endTime;
      workflow.averageExecutionTime = this.calculateAverageExecutionTime(workflow);
      
      this.emit('execution:completed', execution);
    } catch (error) {
      execution.status = 'failed';
      execution.endTime = new Date();
      execution.error = error instanceof Error ? error.message : String(error);
      
      this.emit('execution:failed', execution);
    }

    return execution;
  }

  async cancelExecution(executionId: string): Promise<boolean> {
    const execution = this.executions.get(executionId);
    if (!execution || execution.status !== 'running') {
      return false;
    }

    execution.status = 'cancelled';
    execution.endTime = new Date();
    
    this.emit('execution:cancelled', execution);
    return true;
  }

  getExecution(executionId: string): WorkflowExecution | null {
    return this.executions.get(executionId) || null;
  }

  getWorkflowExecutions(workflowId: string): WorkflowExecution[] {
    return Array.from(this.executions.values())
      .filter(execution => execution.workflowId === workflowId);
  }

  // Node Execution
  private async executeWorkflowNodes(workflow: Workflow, execution: WorkflowExecution): Promise<void> {
    const executionOrder = this.calculateExecutionOrder(workflow);
    
    for (const nodeId of executionOrder) {
      const node = workflow.nodes.find(n => n.id === nodeId);
      if (!node || !node.enabled) continue;

      const nodeExecution = await this.executeNode(node, workflow, execution);
      execution.nodeExecutions.push(nodeExecution);

      if (nodeExecution.status === 'failed') {
        throw new Error(`Node ${node.name} failed: ${nodeExecution.error}`);
      }

      // Check if execution was cancelled
      if (execution.status === 'cancelled') {
        break;
      }
    }
  }

  private async executeNode(
    node: WorkflowNode, 
    workflow: Workflow, 
    execution: WorkflowExecution
  ): Promise<NodeExecution> {
    const nodeExecution: NodeExecution = {
      nodeId: node.id,
      startTime: new Date(),
      status: 'running',
      inputs: {},
      outputs: {},
      logs: []
    };

    try {
      // Resolve input values
      nodeExecution.inputs = await this.resolveNodeInputs(node, workflow, execution);

      // Execute node
      const handler = this.nodeHandlers.get(node.type);
      if (!handler) {
        throw new Error(`No handler found for node type: ${node.type}`);
      }

      nodeExecution.outputs = await handler.execute(node, nodeExecution.inputs, execution);
      nodeExecution.status = 'completed';
      
    } catch (error) {
      nodeExecution.status = 'failed';
      nodeExecution.error = error instanceof Error ? error.message : String(error);
    }

    nodeExecution.endTime = new Date();
    return nodeExecution;
  }

  private async resolveNodeInputs(
    node: WorkflowNode, 
    workflow: Workflow, 
    execution: WorkflowExecution
  ): Promise<Record<string, any>> {
    const inputs: Record<string, any> = {};

    // Get connections to this node
    const incomingConnections = workflow.connections.filter(
      conn => conn.targetNodeId === node.id
    );

    for (const input of node.inputs) {
      const connection = incomingConnections.find(
        conn => conn.targetInputId === input.id
      );

      if (connection) {
        // Get value from connected node output
        const sourceExecution = execution.nodeExecutions.find(
          ne => ne.nodeId === connection.sourceNodeId
        );
        
        if (sourceExecution) {
          inputs[input.name] = sourceExecution.outputs[connection.sourceOutputId];
        }
      } else if (input.defaultValue !== undefined) {
        inputs[input.name] = input.defaultValue;
      } else if (input.required) {
        throw new Error(`Required input '${input.name}' not connected for node '${node.name}'`);
      }
    }

    return inputs;
  }

  private calculateExecutionOrder(workflow: Workflow): string[] {
    // Topological sort to determine execution order
    const visited = new Set<string>();
    const visiting = new Set<string>();
    const order: string[] = [];

    const visit = (nodeId: string) => {
      if (visiting.has(nodeId)) {
        throw new Error('Circular dependency detected in workflow');
      }
      if (visited.has(nodeId)) return;

      visiting.add(nodeId);

      // Visit dependencies first
      const dependencies = workflow.connections
        .filter(conn => conn.targetNodeId === nodeId)
        .map(conn => conn.sourceNodeId);

      for (const depId of dependencies) {
        visit(depId);
      }

      visiting.delete(nodeId);
      visited.add(nodeId);
      order.push(nodeId);
    };

    // Start with nodes that have no dependencies
    const rootNodes = workflow.nodes
      .filter(node => !workflow.connections.some(conn => conn.targetNodeId === node.id))
      .map(node => node.id);

    for (const rootId of rootNodes) {
      visit(rootId);
    }

    return order;
  }

  // Node Handlers
  private initializeNodeHandlers(): void {
    this.nodeHandlers.set('trigger', new TriggerNodeHandler());
    this.nodeHandlers.set('equipment', new EquipmentNodeHandler());
    this.nodeHandlers.set('imaging', new ImagingNodeHandler());
    this.nodeHandlers.set('processing', new ProcessingNodeHandler());
    this.nodeHandlers.set('condition', new ConditionNodeHandler());
    this.nodeHandlers.set('action', new ActionNodeHandler());
    this.nodeHandlers.set('notification', new NotificationNodeHandler());
    this.nodeHandlers.set('delay', new DelayNodeHandler());
    this.nodeHandlers.set('loop', new LoopNodeHandler());
    this.nodeHandlers.set('script', new ScriptNodeHandler());
  }

  registerNodeHandler(type: WorkflowNodeType, handler: NodeHandler): void {
    this.nodeHandlers.set(type, handler);
  }

  // Event System
  on(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  off(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(data));
    }
  }

  // Utility Methods
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private calculateAverageExecutionTime(workflow: Workflow): number {
    const executions = this.getWorkflowExecutions(workflow.id)
      .filter(e => e.status === 'completed' && e.endTime);
    
    if (executions.length === 0) return 0;

    const totalTime = executions.reduce((sum, e) => {
      return sum + (e.endTime!.getTime() - e.startTime.getTime());
    }, 0);

    return totalTime / executions.length;
  }

  // Workflow Validation
  validateWorkflow(workflow: Workflow): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for orphaned nodes
    const connectedNodes = new Set([
      ...workflow.connections.map(c => c.sourceNodeId),
      ...workflow.connections.map(c => c.targetNodeId)
    ]);

    const orphanedNodes = workflow.nodes.filter(node => 
      !connectedNodes.has(node.id) && node.type !== 'trigger'
    );

    if (orphanedNodes.length > 0) {
      errors.push(`Orphaned nodes found: ${orphanedNodes.map(n => n.name).join(', ')}`);
    }

    // Check for circular dependencies
    try {
      this.calculateExecutionOrder(workflow);
    } catch (error) {
      errors.push('Circular dependency detected');
    }

    // Check required inputs
    for (const node of workflow.nodes) {
      for (const input of node.inputs.filter(i => i.required)) {
        const hasConnection = workflow.connections.some(
          c => c.targetNodeId === node.id && c.targetInputId === input.id
        );
        
        if (!hasConnection && input.defaultValue === undefined) {
          errors.push(`Node '${node.name}' missing required input '${input.name}'`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// Abstract base class for node handlers
export abstract class NodeHandler {
  abstract execute(
    node: WorkflowNode, 
    inputs: Record<string, any>, 
    execution: WorkflowExecution
  ): Promise<Record<string, any>>;
}

// Concrete node handler implementations
class TriggerNodeHandler extends NodeHandler {
  async execute(node: WorkflowNode, inputs: Record<string, any>): Promise<Record<string, any>> {
    return { triggered: true, timestamp: new Date() };
  }
}

class EquipmentNodeHandler extends NodeHandler {
  async execute(node: WorkflowNode, inputs: Record<string, any>): Promise<Record<string, any>> {
    // Equipment control logic would go here
    return { status: 'connected', equipment: inputs.equipment };
  }
}

class ImagingNodeHandler extends NodeHandler {
  async execute(node: WorkflowNode, inputs: Record<string, any>): Promise<Record<string, any>> {
    // Imaging logic would go here
    return { images: [], status: 'completed' };
  }
}

class ProcessingNodeHandler extends NodeHandler {
  async execute(node: WorkflowNode, inputs: Record<string, any>): Promise<Record<string, any>> {
    // Image processing logic would go here
    return { processedImages: [], status: 'completed' };
  }
}

class ConditionNodeHandler extends NodeHandler {
  async execute(node: WorkflowNode, inputs: Record<string, any>): Promise<Record<string, any>> {
    // Condition evaluation logic would go here
    return { result: true };
  }
}

class ActionNodeHandler extends NodeHandler {
  async execute(node: WorkflowNode, inputs: Record<string, any>): Promise<Record<string, any>> {
    // Action execution logic would go here
    return { status: 'completed' };
  }
}

class NotificationNodeHandler extends NodeHandler {
  async execute(node: WorkflowNode, inputs: Record<string, any>): Promise<Record<string, any>> {
    // Notification logic would go here
    return { sent: true, timestamp: new Date() };
  }
}

class DelayNodeHandler extends NodeHandler {
  async execute(node: WorkflowNode, inputs: Record<string, any>): Promise<Record<string, any>> {
    const delay = inputs.delay || node.config.delay || 1000;
    await new Promise(resolve => setTimeout(resolve, delay));
    return { delayed: delay };
  }
}

class LoopNodeHandler extends NodeHandler {
  async execute(node: WorkflowNode, inputs: Record<string, any>): Promise<Record<string, any>> {
    // Loop logic would go here
    return { iterations: inputs.iterations || 1 };
  }
}

class ScriptNodeHandler extends NodeHandler {
  async execute(node: WorkflowNode, inputs: Record<string, any>): Promise<Record<string, any>> {
    // Script execution logic would go here
    return { result: 'script executed' };
  }
}
