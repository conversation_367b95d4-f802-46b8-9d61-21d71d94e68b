import { EquipmentProfile } from '../stores/equipment-store';

export interface EquipmentHealthData {
  equipmentId: string;
  timestamp: Date;
  metrics: HealthMetrics;
  operatingConditions: OperatingConditions;
  performanceIndicators: PerformanceIndicators;
  anomalies: Anomaly[];
}

export interface HealthMetrics {
  temperature: number; // Celsius
  humidity: number; // percentage
  vibration: number; // acceleration in g
  power: {
    voltage: number; // volts
    current: number; // amperes
    consumption: number; // watts
  };
  mechanical: {
    backlash: number; // arcseconds
    tracking: number; // RMS error in arcseconds
    positioning: number; // accuracy in arcseconds
  };
  optical: {
    collimation: number; // quality score 0-100
    focus: number; // HFR in pixels
    throughput: number; // percentage of expected
  };
  electronic: {
    signalNoise: number; // SNR
    darkCurrent: number; // e-/pixel/second
    readNoise: number; // e- RMS
  };
}

export interface OperatingConditions {
  ambientTemperature: number;
  ambientHumidity: number;
  windSpeed: number; // m/s
  pressure: number; // hPa
  dewPoint: number;
  operatingTime: number; // hours since last power on
  totalOperatingTime: number; // total lifetime hours
  cycleCount: number; // number of operations
}

export interface PerformanceIndicators {
  imageQuality: number; // 0-100 score
  trackingAccuracy: number; // RMS error
  focusStability: number; // variation over time
  thermalStability: number; // temperature variation
  mechanicalStability: number; // positioning repeatability
  overallHealth: number; // 0-100 composite score
}

export interface Anomaly {
  id: string;
  type: AnomalyType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  detectedAt: Date;
  metric: string;
  value: number;
  expectedRange: { min: number; max: number };
  confidence: number; // 0-100
  trend: 'improving' | 'stable' | 'degrading';
}

export type AnomalyType = 
  | 'temperature_spike'
  | 'vibration_increase'
  | 'tracking_drift'
  | 'focus_instability'
  | 'power_fluctuation'
  | 'mechanical_wear'
  | 'optical_degradation'
  | 'electronic_noise';

export interface MaintenanceRecommendation {
  id: string;
  equipmentId: string;
  type: MaintenanceType;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  description: string;
  estimatedDuration: number; // minutes
  estimatedCost: number; // dollars
  requiredParts: MaintenancePart[];
  requiredTools: string[];
  skillLevel: 'basic' | 'intermediate' | 'advanced' | 'professional';
  scheduledDate?: Date;
  dueDate?: Date;
  predictedFailureDate?: Date;
  preventedIssues: string[];
  instructions: MaintenanceInstruction[];
  riskIfDeferred: string;
  confidence: number; // 0-100
}

export type MaintenanceType = 
  | 'cleaning'
  | 'lubrication'
  | 'calibration'
  | 'alignment'
  | 'replacement'
  | 'inspection'
  | 'software_update'
  | 'preventive'
  | 'corrective';

export interface MaintenancePart {
  name: string;
  partNumber?: string;
  quantity: number;
  estimatedCost: number;
  supplier?: string;
  availability: 'in_stock' | 'order_required' | 'discontinued';
}

export interface MaintenanceInstruction {
  step: number;
  title: string;
  description: string;
  warnings?: string[];
  images?: string[];
  estimatedTime: number; // minutes
  requiredTools: string[];
}

export interface EquipmentLifecycle {
  equipmentId: string;
  purchaseDate: Date;
  warrantyExpiration?: Date;
  expectedLifespan: number; // years
  currentAge: number; // years
  utilizationRate: number; // hours per year
  maintenanceHistory: MaintenanceRecord[];
  replacementCost: number;
  depreciation: DepreciationData;
  lifecycle: LifecyclePhase;
  recommendations: LifecycleRecommendation[];
}

export interface MaintenanceRecord {
  id: string;
  date: Date;
  type: MaintenanceType;
  description: string;
  duration: number; // minutes
  cost: number;
  partsReplaced: string[];
  performedBy: string;
  notes?: string;
  beforeMetrics?: HealthMetrics;
  afterMetrics?: HealthMetrics;
  effectiveness: number; // 0-100 improvement score
}

export interface DepreciationData {
  originalValue: number;
  currentValue: number;
  depreciationRate: number; // per year
  residualValue: number;
  totalCostOfOwnership: number;
}

export type LifecyclePhase = 
  | 'new' // 0-1 years
  | 'prime' // 1-5 years
  | 'mature' // 5-10 years
  | 'aging' // 10-15 years
  | 'end_of_life'; // 15+ years

export interface LifecycleRecommendation {
  type: 'upgrade' | 'replace' | 'maintain' | 'retire';
  reason: string;
  timeframe: string;
  estimatedCost: number;
  expectedBenefit: string;
  riskOfInaction: string;
}

export interface PredictiveModel {
  equipmentType: string;
  modelVersion: string;
  accuracy: number; // 0-100
  trainingData: {
    samples: number;
    timeRange: { start: Date; end: Date };
    features: string[];
  };
  predictions: {
    failureProbability: number; // 0-100
    timeToFailure: number; // days
    confidence: number; // 0-100
    factors: PredictionFactor[];
  };
}

export interface PredictionFactor {
  factor: string;
  impact: number; // -100 to 100
  trend: 'improving' | 'stable' | 'degrading';
  description: string;
}

export class PredictiveMaintenanceAnalyzer {
  private healthData: Map<string, EquipmentHealthData[]> = new Map();
  private models: Map<string, PredictiveModel> = new Map();
  private recommendations: Map<string, MaintenanceRecommendation[]> = new Map();
  private lifecycles: Map<string, EquipmentLifecycle> = new Map();

  // Data Collection
  recordHealthData(data: EquipmentHealthData): void {
    const equipmentId = data.equipmentId;
    
    if (!this.healthData.has(equipmentId)) {
      this.healthData.set(equipmentId, []);
    }
    
    const history = this.healthData.get(equipmentId)!;
    history.push(data);
    
    // Keep only last 1000 records per equipment
    if (history.length > 1000) {
      history.shift();
    }
    
    // Trigger analysis
    this.analyzeEquipmentHealth(equipmentId);
  }

  // Health Analysis
  private analyzeEquipmentHealth(equipmentId: string): void {
    const history = this.healthData.get(equipmentId);
    if (!history || history.length < 10) return;

    const latest = history[history.length - 1];
    const anomalies = this.detectAnomalies(equipmentId, latest, history);
    
    // Update latest data with detected anomalies
    latest.anomalies = anomalies;
    
    // Generate recommendations based on anomalies
    this.generateMaintenanceRecommendations(equipmentId, anomalies, history);
    
    // Update predictive model
    this.updatePredictiveModel(equipmentId, history);
  }

  private detectAnomalies(
    equipmentId: string, 
    current: EquipmentHealthData, 
    history: EquipmentHealthData[]
  ): Anomaly[] {
    const anomalies: Anomaly[] = [];
    
    // Calculate baseline statistics from recent history
    const recentHistory = history.slice(-50); // Last 50 readings
    const baselines = this.calculateBaselines(recentHistory);
    
    // Temperature anomalies
    if (current.metrics.temperature > baselines.temperature.max + 5) {
      anomalies.push({
        id: this.generateId(),
        type: 'temperature_spike',
        severity: current.metrics.temperature > baselines.temperature.max + 10 ? 'high' : 'medium',
        description: `Temperature ${current.metrics.temperature}°C exceeds normal range`,
        detectedAt: current.timestamp,
        metric: 'temperature',
        value: current.metrics.temperature,
        expectedRange: { min: baselines.temperature.min, max: baselines.temperature.max },
        confidence: 85,
        trend: this.calculateTrend(history.map(h => h.metrics.temperature))
      });
    }

    // Vibration anomalies
    if (current.metrics.vibration > baselines.vibration.max * 1.5) {
      anomalies.push({
        id: this.generateId(),
        type: 'vibration_increase',
        severity: current.metrics.vibration > baselines.vibration.max * 2 ? 'high' : 'medium',
        description: `Vibration ${current.metrics.vibration}g exceeds normal levels`,
        detectedAt: current.timestamp,
        metric: 'vibration',
        value: current.metrics.vibration,
        expectedRange: { min: 0, max: baselines.vibration.max },
        confidence: 90,
        trend: this.calculateTrend(history.map(h => h.metrics.vibration))
      });
    }

    // Tracking accuracy anomalies
    if (current.metrics.mechanical.tracking > baselines.tracking.max * 1.3) {
      anomalies.push({
        id: this.generateId(),
        type: 'tracking_drift',
        severity: current.metrics.mechanical.tracking > baselines.tracking.max * 2 ? 'high' : 'medium',
        description: `Tracking error ${current.metrics.mechanical.tracking}" exceeds acceptable limits`,
        detectedAt: current.timestamp,
        metric: 'tracking',
        value: current.metrics.mechanical.tracking,
        expectedRange: { min: 0, max: baselines.tracking.max },
        confidence: 88,
        trend: this.calculateTrend(history.map(h => h.metrics.mechanical.tracking))
      });
    }

    // Focus stability anomalies
    if (current.metrics.optical.focus > baselines.focus.max * 1.2) {
      anomalies.push({
        id: this.generateId(),
        type: 'focus_instability',
        severity: current.metrics.optical.focus > baselines.focus.max * 1.5 ? 'high' : 'medium',
        description: `Focus HFR ${current.metrics.optical.focus} pixels indicates instability`,
        detectedAt: current.timestamp,
        metric: 'focus',
        value: current.metrics.optical.focus,
        expectedRange: { min: baselines.focus.min, max: baselines.focus.max },
        confidence: 82,
        trend: this.calculateTrend(history.map(h => h.metrics.optical.focus))
      });
    }

    // Power anomalies
    const powerVariation = Math.abs(current.metrics.power.consumption - baselines.power.avg);
    if (powerVariation > baselines.power.std * 3) {
      anomalies.push({
        id: this.generateId(),
        type: 'power_fluctuation',
        severity: powerVariation > baselines.power.std * 5 ? 'high' : 'medium',
        description: `Power consumption ${current.metrics.power.consumption}W deviates significantly`,
        detectedAt: current.timestamp,
        metric: 'power',
        value: current.metrics.power.consumption,
        expectedRange: { 
          min: baselines.power.avg - baselines.power.std * 2, 
          max: baselines.power.avg + baselines.power.std * 2 
        },
        confidence: 87,
        trend: this.calculateTrend(history.map(h => h.metrics.power.consumption))
      });
    }

    return anomalies;
  }

  private calculateBaselines(history: EquipmentHealthData[]): any {
    const temperatures = history.map(h => h.metrics.temperature);
    const vibrations = history.map(h => h.metrics.vibration);
    const tracking = history.map(h => h.metrics.mechanical.tracking);
    const focus = history.map(h => h.metrics.optical.focus);
    const power = history.map(h => h.metrics.power.consumption);

    return {
      temperature: {
        min: Math.min(...temperatures),
        max: Math.max(...temperatures),
        avg: temperatures.reduce((a, b) => a + b, 0) / temperatures.length
      },
      vibration: {
        min: Math.min(...vibrations),
        max: Math.max(...vibrations),
        avg: vibrations.reduce((a, b) => a + b, 0) / vibrations.length
      },
      tracking: {
        min: Math.min(...tracking),
        max: Math.max(...tracking),
        avg: tracking.reduce((a, b) => a + b, 0) / tracking.length
      },
      focus: {
        min: Math.min(...focus),
        max: Math.max(...focus),
        avg: focus.reduce((a, b) => a + b, 0) / focus.length
      },
      power: {
        avg: power.reduce((a, b) => a + b, 0) / power.length,
        std: Math.sqrt(power.reduce((sum, val) => {
          const avg = power.reduce((a, b) => a + b, 0) / power.length;
          return sum + Math.pow(val - avg, 2);
        }, 0) / power.length)
      }
    };
  }

  private calculateTrend(values: number[]): 'improving' | 'stable' | 'degrading' {
    if (values.length < 5) return 'stable';
    
    const recent = values.slice(-5);
    const older = values.slice(-10, -5);
    
    if (older.length === 0) return 'stable';
    
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const olderAvg = older.reduce((a, b) => a + b, 0) / older.length;
    
    const change = (recentAvg - olderAvg) / olderAvg;
    
    if (change > 0.1) return 'degrading';
    if (change < -0.1) return 'improving';
    return 'stable';
  }

  // Maintenance Recommendations
  private generateMaintenanceRecommendations(
    equipmentId: string, 
    anomalies: Anomaly[], 
    history: EquipmentHealthData[]
  ): void {
    const recommendations: MaintenanceRecommendation[] = [];
    
    for (const anomaly of anomalies) {
      const recommendation = this.createRecommendationForAnomaly(equipmentId, anomaly);
      if (recommendation) {
        recommendations.push(recommendation);
      }
    }
    
    // Add preventive maintenance recommendations
    const preventive = this.generatePreventiveRecommendations(equipmentId, history);
    recommendations.push(...preventive);
    
    this.recommendations.set(equipmentId, recommendations);
  }

  private createRecommendationForAnomaly(
    equipmentId: string, 
    anomaly: Anomaly
  ): MaintenanceRecommendation | null {
    switch (anomaly.type) {
      case 'temperature_spike':
        return {
          id: this.generateId(),
          equipmentId,
          type: 'inspection',
          priority: anomaly.severity === 'high' ? 'urgent' : 'high',
          title: 'Temperature Investigation Required',
          description: 'Investigate cause of elevated temperature and improve cooling',
          estimatedDuration: 60,
          estimatedCost: 50,
          requiredParts: [],
          requiredTools: ['thermometer', 'compressed air'],
          skillLevel: 'intermediate',
          dueDate: new Date(Date.now() + (anomaly.severity === 'high' ? 1 : 3) * 24 * 60 * 60 * 1000),
          preventedIssues: ['thermal damage', 'component failure', 'image quality degradation'],
          instructions: [
            {
              step: 1,
              title: 'Check ventilation',
              description: 'Inspect all cooling fans and air vents for obstructions',
              estimatedTime: 15,
              requiredTools: ['flashlight']
            },
            {
              step: 2,
              title: 'Clean components',
              description: 'Use compressed air to remove dust from heat sinks and fans',
              estimatedTime: 30,
              requiredTools: ['compressed air'],
              warnings: ['Ensure equipment is powered off before cleaning']
            }
          ],
          riskIfDeferred: 'Continued high temperatures may cause permanent component damage',
          confidence: 85
        };

      case 'vibration_increase':
        return {
          id: this.generateId(),
          equipmentId,
          type: 'inspection',
          priority: 'high',
          title: 'Vibration Analysis and Stabilization',
          description: 'Investigate source of increased vibration and improve stability',
          estimatedDuration: 90,
          estimatedCost: 100,
          requiredParts: [
            { name: 'Vibration dampeners', quantity: 4, estimatedCost: 40, availability: 'order_required' }
          ],
          requiredTools: ['torque wrench', 'level', 'vibration meter'],
          skillLevel: 'advanced',
          dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
          preventedIssues: ['tracking errors', 'mechanical wear', 'image blur'],
          instructions: [
            {
              step: 1,
              title: 'Check mount stability',
              description: 'Verify all mounting bolts are properly tightened',
              estimatedTime: 20,
              requiredTools: ['torque wrench']
            },
            {
              step: 2,
              title: 'Install dampeners',
              description: 'Add vibration dampening materials to reduce resonance',
              estimatedTime: 45,
              requiredTools: ['screwdriver', 'level']
            }
          ],
          riskIfDeferred: 'Increased vibration will degrade tracking accuracy and image quality',
          confidence: 90
        };

      case 'tracking_drift':
        return {
          id: this.generateId(),
          equipmentId,
          type: 'calibration',
          priority: 'high',
          title: 'Mount Calibration and Alignment',
          description: 'Recalibrate mount and check polar alignment',
          estimatedDuration: 120,
          estimatedCost: 0,
          requiredParts: [],
          requiredTools: ['polar scope', 'alignment software'],
          skillLevel: 'intermediate',
          dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
          preventedIssues: ['poor tracking', 'star trailing', 'guiding issues'],
          instructions: [
            {
              step: 1,
              title: 'Polar alignment',
              description: 'Perform precise polar alignment using polar scope or software',
              estimatedTime: 60,
              requiredTools: ['polar scope']
            },
            {
              step: 2,
              title: 'Calibrate mount',
              description: 'Run mount calibration routine and update parameters',
              estimatedTime: 45,
              requiredTools: ['alignment software']
            }
          ],
          riskIfDeferred: 'Poor tracking will result in unusable long-exposure images',
          confidence: 88
        };

      default:
        return null;
    }
  }

  private generatePreventiveRecommendations(
    equipmentId: string, 
    history: EquipmentHealthData[]
  ): MaintenanceRecommendation[] {
    const recommendations: MaintenanceRecommendation[] = [];
    
    if (history.length === 0) return recommendations;
    
    const latest = history[history.length - 1];
    const totalHours = latest.operatingConditions.totalOperatingTime;
    
    // Cleaning recommendations based on operating time
    if (totalHours > 0 && totalHours % 100 < 10) { // Every ~100 hours
      recommendations.push({
        id: this.generateId(),
        equipmentId,
        type: 'cleaning',
        priority: 'medium',
        title: 'Routine Cleaning',
        description: 'Perform routine cleaning of optical and mechanical components',
        estimatedDuration: 45,
        estimatedCost: 25,
        requiredParts: [
          { name: 'Optical cleaning kit', quantity: 1, estimatedCost: 25, availability: 'in_stock' }
        ],
        requiredTools: ['microfiber cloths', 'cleaning solution', 'compressed air'],
        skillLevel: 'basic',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        preventedIssues: ['dust accumulation', 'optical degradation', 'mechanical binding'],
        instructions: [
          {
            step: 1,
            title: 'Clean optics',
            description: 'Carefully clean all optical surfaces with appropriate materials',
            estimatedTime: 30,
            requiredTools: ['microfiber cloths', 'cleaning solution'],
            warnings: ['Never use paper towels on optical surfaces']
          }
        ],
        riskIfDeferred: 'Dust accumulation will gradually degrade image quality',
        confidence: 95
      });
    }
    
    return recommendations;
  }

  // Predictive Modeling
  private updatePredictiveModel(equipmentId: string, history: EquipmentHealthData[]): void {
    if (history.length < 50) return; // Need sufficient data
    
    const model: PredictiveModel = {
      equipmentType: 'generic', // Would be determined from equipment profile
      modelVersion: '1.0',
      accuracy: 75, // Would be calculated from validation data
      trainingData: {
        samples: history.length,
        timeRange: {
          start: history[0].timestamp,
          end: history[history.length - 1].timestamp
        },
        features: ['temperature', 'vibration', 'tracking', 'focus', 'power']
      },
      predictions: this.generatePredictions(history)
    };
    
    this.models.set(equipmentId, model);
  }

  private generatePredictions(history: EquipmentHealthData[]): PredictiveModel['predictions'] {
    // Simplified prediction logic - in reality would use ML models
    const latest = history[history.length - 1];
    const trends = this.analyzeTrends(history);
    
    let failureProbability = 0;
    let timeToFailure = 365; // days
    const factors: PredictionFactor[] = [];
    
    // Analyze each factor
    if (trends.temperature.slope > 0.1) {
      failureProbability += 20;
      timeToFailure = Math.min(timeToFailure, 180);
      factors.push({
        factor: 'Rising Temperature',
        impact: 25,
        trend: 'degrading',
        description: 'Temperature trend indicates potential cooling system issues'
      });
    }
    
    if (trends.vibration.slope > 0.05) {
      failureProbability += 15;
      timeToFailure = Math.min(timeToFailure, 120);
      factors.push({
        factor: 'Increasing Vibration',
        impact: 20,
        trend: 'degrading',
        description: 'Vibration increase suggests mechanical wear or loosening'
      });
    }
    
    if (trends.tracking.slope > 0.02) {
      failureProbability += 10;
      timeToFailure = Math.min(timeToFailure, 90);
      factors.push({
        factor: 'Tracking Degradation',
        impact: 15,
        trend: 'degrading',
        description: 'Tracking accuracy decline indicates mount or drive issues'
      });
    }
    
    // Operating time factor
    const operatingHours = latest.operatingConditions.totalOperatingTime;
    if (operatingHours > 5000) {
      failureProbability += Math.min(30, (operatingHours - 5000) / 100);
      factors.push({
        factor: 'Operating Hours',
        impact: Math.min(30, (operatingHours - 5000) / 100),
        trend: 'stable',
        description: `High operating hours (${operatingHours}h) increase wear probability`
      });
    }
    
    return {
      failureProbability: Math.min(100, failureProbability),
      timeToFailure,
      confidence: 70,
      factors
    };
  }

  private analyzeTrends(history: EquipmentHealthData[]): any {
    const n = history.length;
    if (n < 10) return {};
    
    // Simple linear regression for trend analysis
    const calculateSlope = (values: number[]) => {
      const xSum = values.reduce((sum, _, i) => sum + i, 0);
      const ySum = values.reduce((sum, val) => sum + val, 0);
      const xySum = values.reduce((sum, val, i) => sum + i * val, 0);
      const x2Sum = values.reduce((sum, _, i) => sum + i * i, 0);
      
      return (n * xySum - xSum * ySum) / (n * x2Sum - xSum * xSum);
    };
    
    return {
      temperature: { slope: calculateSlope(history.map(h => h.metrics.temperature)) },
      vibration: { slope: calculateSlope(history.map(h => h.metrics.vibration)) },
      tracking: { slope: calculateSlope(history.map(h => h.metrics.mechanical.tracking)) },
      focus: { slope: calculateSlope(history.map(h => h.metrics.optical.focus)) }
    };
  }

  // Public API Methods
  getEquipmentHealth(equipmentId: string): EquipmentHealthData | null {
    const history = this.healthData.get(equipmentId);
    return history ? history[history.length - 1] : null;
  }

  getHealthHistory(equipmentId: string, days: number = 30): EquipmentHealthData[] {
    const history = this.healthData.get(equipmentId) || [];
    const cutoff = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return history.filter(h => h.timestamp >= cutoff);
  }

  getMaintenanceRecommendations(equipmentId: string): MaintenanceRecommendation[] {
    return this.recommendations.get(equipmentId) || [];
  }

  getPredictiveModel(equipmentId: string): PredictiveModel | null {
    return this.models.get(equipmentId) || null;
  }

  getEquipmentLifecycle(equipmentId: string): EquipmentLifecycle | null {
    return this.lifecycles.get(equipmentId) || null;
  }

  // Utility Methods
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  // Lifecycle Management
  initializeEquipmentLifecycle(equipment: EquipmentProfile): EquipmentLifecycle {
    const lifecycle: EquipmentLifecycle = {
      equipmentId: equipment.id,
      purchaseDate: new Date(), // Would come from equipment data
      expectedLifespan: this.getExpectedLifespan(equipment),
      currentAge: 0,
      utilizationRate: 0,
      maintenanceHistory: [],
      replacementCost: this.estimateReplacementCost(equipment),
      depreciation: {
        originalValue: this.estimateReplacementCost(equipment),
        currentValue: this.estimateReplacementCost(equipment),
        depreciationRate: 0.15, // 15% per year
        residualValue: this.estimateReplacementCost(equipment) * 0.1,
        totalCostOfOwnership: this.estimateReplacementCost(equipment)
      },
      lifecycle: 'new',
      recommendations: []
    };

    this.lifecycles.set(equipment.id, lifecycle);
    return lifecycle;
  }

  private getExpectedLifespan(equipment: EquipmentProfile): number {
    // Estimate based on equipment type
    if (equipment.telescope) return 20; // years
    if (equipment.camera) return 10;
    if (equipment.mount) return 15;
    return 10; // default
  }

  private estimateReplacementCost(equipment: EquipmentProfile): number {
    // Simplified cost estimation
    let cost = 0;
    if (equipment.telescope) cost += 5000;
    if (equipment.camera) cost += 3000;
    if (equipment.mount) cost += 4000;
    return Math.max(1000, cost);
  }
}
