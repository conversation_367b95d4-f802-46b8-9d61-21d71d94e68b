import { Target } from '../target-planning/target-database';
import { EquipmentProfile } from '../stores/equipment-store';

export interface Observatory {
  id: string;
  name: string;
  description?: string;
  location: {
    latitude: number;
    longitude: number;
    elevation: number; // meters
    timezone: string;
    name: string;
  };
  owner: string; // user ID
  members: ObservatoryMember[];
  equipment: EquipmentProfile[];
  settings: ObservatorySettings;
  created: Date;
  modified: Date;
  isPublic: boolean;
  inviteCode?: string;
  statistics: ObservatoryStatistics;
}

export interface ObservatoryMember {
  userId: string;
  username: string;
  email: string;
  role: ObservatoryRole;
  permissions: Permission[];
  joinedDate: Date;
  lastActive: Date;
  status: 'active' | 'inactive' | 'suspended';
  preferences: MemberPreferences;
}

export interface ObservatorySettings {
  allowGuestAccess: boolean;
  requireApprovalForJoin: boolean;
  maxConcurrentSessions: number;
  sessionTimeLimit: number; // minutes
  equipmentBookingAdvance: number; // days
  autoReleaseInactive: number; // minutes
  notifications: {
    sessionStart: boolean;
    sessionEnd: boolean;
    equipmentIssues: boolean;
    weatherAlerts: boolean;
    memberActivity: boolean;
  };
  scheduling: {
    allowOverlapping: boolean;
    prioritySystem: 'first-come' | 'role-based' | 'rotation';
    advanceBookingLimit: number; // days
  };
}

export interface ObservatoryStatistics {
  totalSessions: number;
  totalImagingTime: number; // hours
  activeMembers: number;
  equipmentUtilization: number; // percentage
  averageSessionDuration: number; // hours
  mostPopularTargets: string[];
  peakUsageHours: number[];
}

export interface MemberPreferences {
  notifications: {
    email: boolean;
    push: boolean;
    inApp: boolean;
  };
  scheduling: {
    preferredTimeSlots: TimeSlot[];
    maxSessionDuration: number;
    autoExtendSessions: boolean;
  };
  privacy: {
    shareSessionData: boolean;
    showOnlineStatus: boolean;
    allowDirectMessages: boolean;
  };
}

export interface TimeSlot {
  dayOfWeek: number; // 0-6, Sunday = 0
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
}

export type ObservatoryRole = 'owner' | 'admin' | 'operator' | 'observer' | 'guest';

export type Permission = 
  | 'manage_members'
  | 'manage_equipment'
  | 'manage_settings'
  | 'book_sessions'
  | 'control_equipment'
  | 'view_sessions'
  | 'view_analytics'
  | 'manage_targets'
  | 'export_data'
  | 'send_notifications';

export interface SessionBooking {
  id: string;
  observatoryId: string;
  userId: string;
  username: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  status: 'scheduled' | 'active' | 'completed' | 'cancelled' | 'no-show';
  equipment: string[]; // equipment IDs
  targets: Target[];
  priority: number; // 1-10
  isRecurring: boolean;
  recurrencePattern?: RecurrencePattern;
  notifications: BookingNotification[];
  created: Date;
  modified: Date;
  actualStartTime?: Date;
  actualEndTime?: Date;
  notes?: string;
  rating?: number; // 1-5 stars
  weatherConditions?: any;
  results?: SessionResults;
}

export interface RecurrencePattern {
  type: 'daily' | 'weekly' | 'monthly';
  interval: number; // every N days/weeks/months
  daysOfWeek?: number[]; // for weekly recurrence
  endDate?: Date;
  maxOccurrences?: number;
}

export interface BookingNotification {
  type: 'reminder' | 'start' | 'end' | 'cancelled' | 'modified';
  sentAt: Date;
  method: 'email' | 'push' | 'sms';
  recipient: string;
}

export interface SessionResults {
  imagesCount: number;
  totalExposureTime: number; // seconds
  averageHFR: number;
  averageSNR: number;
  successRate: number; // 0-100%
  issues: string[];
  notes: string;
}

export interface CollaborationEvent {
  id: string;
  type: 'member_joined' | 'member_left' | 'session_started' | 'session_ended' | 'equipment_issue' | 'message';
  observatoryId: string;
  userId: string;
  username: string;
  timestamp: Date;
  data: Record<string, any>;
  isPublic: boolean;
}

export interface DirectMessage {
  id: string;
  fromUserId: string;
  fromUsername: string;
  toUserId: string;
  toUsername: string;
  message: string;
  timestamp: Date;
  read: boolean;
  type: 'text' | 'image' | 'file' | 'session_invite';
  attachments?: MessageAttachment[];
}

export interface MessageAttachment {
  id: string;
  filename: string;
  size: number;
  type: string;
  url: string;
}

export class ObservatoryManager {
  private observatories: Map<string, Observatory> = new Map();
  private bookings: Map<string, SessionBooking> = new Map();
  private events: CollaborationEvent[] = [];
  private messages: DirectMessage[] = [];
  private eventListeners: Map<string, Function[]> = new Map();

  // Observatory Management
  createObservatory(observatory: Omit<Observatory, 'id' | 'created' | 'modified' | 'statistics'>): Observatory {
    const newObservatory: Observatory = {
      ...observatory,
      id: this.generateId(),
      created: new Date(),
      modified: new Date(),
      statistics: {
        totalSessions: 0,
        totalImagingTime: 0,
        activeMembers: 1,
        equipmentUtilization: 0,
        averageSessionDuration: 0,
        mostPopularTargets: [],
        peakUsageHours: []
      }
    };

    this.observatories.set(newObservatory.id, newObservatory);
    this.emitEvent('observatory:created', newObservatory);
    return newObservatory;
  }

  updateObservatory(observatoryId: string, updates: Partial<Observatory>): Observatory | null {
    const observatory = this.observatories.get(observatoryId);
    if (!observatory) return null;

    const updatedObservatory = {
      ...observatory,
      ...updates,
      modified: new Date()
    };

    this.observatories.set(observatoryId, updatedObservatory);
    this.emitEvent('observatory:updated', updatedObservatory);
    return updatedObservatory;
  }

  deleteObservatory(observatoryId: string): boolean {
    const observatory = this.observatories.get(observatoryId);
    if (!observatory) return false;

    // Cancel all future bookings
    const futureBookings = Array.from(this.bookings.values())
      .filter(booking => 
        booking.observatoryId === observatoryId && 
        booking.startTime > new Date()
      );

    futureBookings.forEach(booking => {
      this.cancelBooking(booking.id, 'Observatory deleted');
    });

    this.observatories.delete(observatoryId);
    this.emitEvent('observatory:deleted', observatory);
    return true;
  }

  getObservatory(observatoryId: string): Observatory | null {
    return this.observatories.get(observatoryId) || null;
  }

  getUserObservatories(userId: string): Observatory[] {
    return Array.from(this.observatories.values())
      .filter(obs => 
        obs.owner === userId || 
        obs.members.some(member => member.userId === userId)
      );
  }

  getPublicObservatories(): Observatory[] {
    return Array.from(this.observatories.values())
      .filter(obs => obs.isPublic);
  }

  // Member Management
  addMember(observatoryId: string, member: Omit<ObservatoryMember, 'joinedDate' | 'lastActive'>): boolean {
    const observatory = this.observatories.get(observatoryId);
    if (!observatory) return false;

    const newMember: ObservatoryMember = {
      ...member,
      joinedDate: new Date(),
      lastActive: new Date()
    };

    observatory.members.push(newMember);
    observatory.statistics.activeMembers = observatory.members.filter(m => m.status === 'active').length;
    observatory.modified = new Date();

    this.observatories.set(observatoryId, observatory);
    this.emitEvent('member:added', { observatory, member: newMember });
    return true;
  }

  removeMember(observatoryId: string, userId: string): boolean {
    const observatory = this.observatories.get(observatoryId);
    if (!observatory) return false;

    const memberIndex = observatory.members.findIndex(m => m.userId === userId);
    if (memberIndex === -1) return false;

    const member = observatory.members[memberIndex];
    observatory.members.splice(memberIndex, 1);
    observatory.statistics.activeMembers = observatory.members.filter(m => m.status === 'active').length;
    observatory.modified = new Date();

    // Cancel user's future bookings
    const userBookings = Array.from(this.bookings.values())
      .filter(booking => 
        booking.observatoryId === observatoryId && 
        booking.userId === userId &&
        booking.startTime > new Date()
      );

    userBookings.forEach(booking => {
      this.cancelBooking(booking.id, 'Member removed from observatory');
    });

    this.observatories.set(observatoryId, observatory);
    this.emitEvent('member:removed', { observatory, member });
    return true;
  }

  updateMemberRole(observatoryId: string, userId: string, role: ObservatoryRole, permissions: Permission[]): boolean {
    const observatory = this.observatories.get(observatoryId);
    if (!observatory) return false;

    const member = observatory.members.find(m => m.userId === userId);
    if (!member) return false;

    member.role = role;
    member.permissions = permissions;
    observatory.modified = new Date();

    this.observatories.set(observatoryId, observatory);
    this.emitEvent('member:role_updated', { observatory, member });
    return true;
  }

  // Session Booking
  createBooking(booking: Omit<SessionBooking, 'id' | 'created' | 'modified' | 'notifications'>): SessionBooking | null {
    // Check for conflicts
    const conflicts = this.checkBookingConflicts(booking.observatoryId, booking.startTime, booking.endTime, booking.equipment);
    if (conflicts.length > 0) {
      return null; // Booking conflicts exist
    }

    const newBooking: SessionBooking = {
      ...booking,
      id: this.generateId(),
      created: new Date(),
      modified: new Date(),
      notifications: []
    };

    this.bookings.set(newBooking.id, newBooking);
    this.emitEvent('booking:created', newBooking);
    
    // Schedule notifications
    this.scheduleBookingNotifications(newBooking);
    
    return newBooking;
  }

  updateBooking(bookingId: string, updates: Partial<SessionBooking>): SessionBooking | null {
    const booking = this.bookings.get(bookingId);
    if (!booking) return null;

    // Check for conflicts if time or equipment changed
    if (updates.startTime || updates.endTime || updates.equipment) {
      const conflicts = this.checkBookingConflicts(
        booking.observatoryId,
        updates.startTime || booking.startTime,
        updates.endTime || booking.endTime,
        updates.equipment || booking.equipment,
        bookingId // exclude current booking
      );
      
      if (conflicts.length > 0) {
        return null; // Conflicts exist
      }
    }

    const updatedBooking = {
      ...booking,
      ...updates,
      modified: new Date()
    };

    this.bookings.set(bookingId, updatedBooking);
    this.emitEvent('booking:updated', updatedBooking);
    return updatedBooking;
  }

  cancelBooking(bookingId: string, reason?: string): boolean {
    const booking = this.bookings.get(bookingId);
    if (!booking) return false;

    booking.status = 'cancelled';
    booking.notes = reason || 'Cancelled by user';
    booking.modified = new Date();

    this.bookings.set(bookingId, booking);
    this.emitEvent('booking:cancelled', booking);
    return true;
  }

  getBooking(bookingId: string): SessionBooking | null {
    return this.bookings.get(bookingId) || null;
  }

  getObservatoryBookings(observatoryId: string, startDate?: Date, endDate?: Date): SessionBooking[] {
    return Array.from(this.bookings.values())
      .filter(booking => {
        if (booking.observatoryId !== observatoryId) return false;
        if (startDate && booking.endTime < startDate) return false;
        if (endDate && booking.startTime > endDate) return false;
        return true;
      })
      .sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
  }

  getUserBookings(userId: string): SessionBooking[] {
    return Array.from(this.bookings.values())
      .filter(booking => booking.userId === userId)
      .sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
  }

  private checkBookingConflicts(
    observatoryId: string,
    startTime: Date,
    endTime: Date,
    equipment: string[],
    excludeBookingId?: string
  ): SessionBooking[] {
    return Array.from(this.bookings.values())
      .filter(booking => {
        if (booking.id === excludeBookingId) return false;
        if (booking.observatoryId !== observatoryId) return false;
        if (booking.status === 'cancelled') return false;
        
        // Check time overlap
        const hasTimeOverlap = startTime < booking.endTime && endTime > booking.startTime;
        if (!hasTimeOverlap) return false;

        // Check equipment overlap
        const hasEquipmentOverlap = equipment.some(eq => booking.equipment.includes(eq));
        return hasEquipmentOverlap;
      });
  }

  private scheduleBookingNotifications(booking: SessionBooking): void {
    // Implementation would schedule actual notifications
    // For now, just add to the booking's notification array
    const reminderTimes = [24 * 60, 60, 15]; // 24 hours, 1 hour, 15 minutes before

    reminderTimes.forEach(minutesBefore => {
      const reminderTime = new Date(booking.startTime.getTime() - minutesBefore * 60 * 1000);
      if (reminderTime > new Date()) {
        // Schedule reminder notification
        setTimeout(() => {
          this.emitEvent('booking:reminder', { booking, minutesBefore });
        }, reminderTime.getTime() - Date.now());
      }
    });
  }

  // Messaging
  sendMessage(message: Omit<DirectMessage, 'id' | 'timestamp' | 'read'>): DirectMessage {
    const newMessage: DirectMessage = {
      ...message,
      id: this.generateId(),
      timestamp: new Date(),
      read: false
    };

    this.messages.push(newMessage);
    this.emitEvent('message:sent', newMessage);
    return newMessage;
  }

  getMessages(userId: string): DirectMessage[] {
    return this.messages
      .filter(msg => msg.fromUserId === userId || msg.toUserId === userId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  markMessageRead(messageId: string): boolean {
    const message = this.messages.find(m => m.id === messageId);
    if (!message) return false;

    message.read = true;
    this.emitEvent('message:read', message);
    return true;
  }

  // Events and Real-time Updates
  addEvent(event: Omit<CollaborationEvent, 'id' | 'timestamp'>): CollaborationEvent {
    const newEvent: CollaborationEvent = {
      ...event,
      id: this.generateId(),
      timestamp: new Date()
    };

    this.events.push(newEvent);
    this.emitEvent('event:added', newEvent);
    return newEvent;
  }

  getObservatoryEvents(observatoryId: string, limit: number = 50): CollaborationEvent[] {
    return this.events
      .filter(event => event.observatoryId === observatoryId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  // Permission Checking
  hasPermission(observatoryId: string, userId: string, permission: Permission): boolean {
    const observatory = this.observatories.get(observatoryId);
    if (!observatory) return false;

    if (observatory.owner === userId) return true;

    const member = observatory.members.find(m => m.userId === userId);
    if (!member || member.status !== 'active') return false;

    return member.permissions.includes(permission);
  }

  // Event System
  on(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  off(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emitEvent(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(data));
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  // Analytics
  getObservatoryAnalytics(observatoryId: string): ObservatoryStatistics | null {
    const observatory = this.observatories.get(observatoryId);
    if (!observatory) return null;

    // Update statistics based on current data
    const bookings = this.getObservatoryBookings(observatoryId);
    const completedBookings = bookings.filter(b => b.status === 'completed');

    observatory.statistics.totalSessions = completedBookings.length;
    observatory.statistics.totalImagingTime = completedBookings.reduce((sum, b) => {
      return sum + ((b.actualEndTime?.getTime() || b.endTime.getTime()) - 
                   (b.actualStartTime?.getTime() || b.startTime.getTime())) / (1000 * 60 * 60);
    }, 0);

    if (completedBookings.length > 0) {
      observatory.statistics.averageSessionDuration = observatory.statistics.totalImagingTime / completedBookings.length;
    }

    // Calculate equipment utilization
    const now = new Date();
    const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const recentBookings = bookings.filter(b => b.startTime >= last30Days);
    const totalPossibleHours = 30 * 24 * observatory.equipment.length;
    const actualUsedHours = recentBookings.reduce((sum, b) => {
      return sum + (b.endTime.getTime() - b.startTime.getTime()) / (1000 * 60 * 60);
    }, 0);
    
    observatory.statistics.equipmentUtilization = totalPossibleHours > 0 ? 
      (actualUsedHours / totalPossibleHours) * 100 : 0;

    return observatory.statistics;
  }
}
