import { Target } from '../target-planning/target-database';
import { EquipmentProfile } from '../stores/equipment-store';
import { WeatherConditions, AstronomicalConditions } from '../weather/weather-service';
import { ImageMetrics } from '../image-processing/image-analyzer';

export interface ImagingSession {
  id: string;
  date: Date;
  startTime: Date;
  endTime: Date;
  duration: number; // minutes
  targets: SessionTarget[];
  equipment: EquipmentProfile;
  weather: WeatherConditions;
  astronomical: AstronomicalConditions;
  location: {
    latitude: number;
    longitude: number;
    name: string;
  };
  notes?: string;
  overallRating: number; // 1-5 stars
  successRate: number; // 0-100%
}

export interface SessionTarget {
  target: Target;
  startTime: Date;
  endTime: Date;
  duration: number; // minutes
  filters: string[];
  exposures: SessionExposure[];
  imageMetrics: ImageMetrics[];
  successRating: number; // 1-5 stars
  issues: string[];
  notes?: string;
}

export interface SessionExposure {
  filter: string;
  exposureTime: number; // seconds
  frameCount: number;
  acceptedFrames: number;
  rejectedFrames: number;
  averageHFR: number;
  averageSNR: number;
  temperature: number;
  timestamp: Date;
}

export interface SessionInsight {
  type: 'success' | 'improvement' | 'warning' | 'tip';
  category: 'equipment' | 'technique' | 'conditions' | 'planning' | 'processing';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommendations: string[];
  relatedSessions?: string[]; // session IDs
  confidence: number; // 0-100%
}

export interface AnalyticsMetrics {
  totalSessions: number;
  totalImagingTime: number; // hours
  averageSessionDuration: number; // hours
  successRate: number; // 0-100%
  mostSuccessfulTargetTypes: string[];
  bestImagingConditions: {
    temperature: { min: number; max: number };
    humidity: { min: number; max: number };
    seeing: { min: number; max: number };
    moonPhase: { min: number; max: number };
  };
  equipmentPerformance: {
    [equipmentId: string]: {
      successRate: number;
      averageHFR: number;
      averageSNR: number;
      reliability: number;
    };
  };
  improvementTrends: {
    hfr: 'improving' | 'stable' | 'declining';
    snr: 'improving' | 'stable' | 'declining';
    successRate: 'improving' | 'stable' | 'declining';
  };
}

export class SessionAnalyzer {
  private sessions: ImagingSession[] = [];
  private insights: SessionInsight[] = [];

  constructor(sessions: ImagingSession[] = []) {
    this.sessions = sessions;
  }

  addSession(session: ImagingSession): void {
    this.sessions.push(session);
    this.generateInsights();
  }

  updateSession(sessionId: string, updates: Partial<ImagingSession>): void {
    const index = this.sessions.findIndex(s => s.id === sessionId);
    if (index !== -1) {
      this.sessions[index] = { ...this.sessions[index], ...updates };
      this.generateInsights();
    }
  }

  getAnalyticsMetrics(): AnalyticsMetrics {
    if (this.sessions.length === 0) {
      return this.getEmptyMetrics();
    }

    const totalImagingTime = this.sessions.reduce((sum, s) => sum + s.duration, 0) / 60;
    const averageSessionDuration = totalImagingTime / this.sessions.length;
    const successRate = this.sessions.reduce((sum, s) => sum + s.successRate, 0) / this.sessions.length;

    // Analyze target types
    const targetTypeSuccess = new Map<string, { total: number; success: number }>();
    this.sessions.forEach(session => {
      session.targets.forEach(target => {
        const type = target.target.type;
        const current = targetTypeSuccess.get(type) || { total: 0, success: 0 };
        current.total++;
        if (target.successRating >= 4) current.success++;
        targetTypeSuccess.set(type, current);
      });
    });

    const mostSuccessfulTargetTypes = Array.from(targetTypeSuccess.entries())
      .map(([type, stats]) => ({ type, rate: stats.success / stats.total }))
      .sort((a, b) => b.rate - a.rate)
      .slice(0, 3)
      .map(item => item.type);

    // Analyze conditions
    const bestConditions = this.analyzeBestConditions();

    // Equipment performance
    const equipmentPerformance = this.analyzeEquipmentPerformance();

    // Improvement trends
    const improvementTrends = this.analyzeImprovementTrends();

    return {
      totalSessions: this.sessions.length,
      totalImagingTime,
      averageSessionDuration,
      successRate,
      mostSuccessfulTargetTypes,
      bestImagingConditions: bestConditions,
      equipmentPerformance,
      improvementTrends
    };
  }

  getInsights(): SessionInsight[] {
    return this.insights;
  }

  getSessionsByDateRange(startDate: Date, endDate: Date): ImagingSession[] {
    return this.sessions.filter(session => 
      session.date >= startDate && session.date <= endDate
    );
  }

  getTargetAnalysis(targetId: string): {
    sessions: ImagingSession[];
    averageSuccessRate: number;
    bestConditions: any;
    recommendations: string[];
  } {
    const targetSessions = this.sessions.filter(session =>
      session.targets.some(t => t.target.id === targetId)
    );

    if (targetSessions.length === 0) {
      return {
        sessions: [],
        averageSuccessRate: 0,
        bestConditions: null,
        recommendations: ['No previous sessions found for this target']
      };
    }

    const successRates = targetSessions.map(session => {
      const targetData = session.targets.find(t => t.target.id === targetId);
      return targetData?.successRating || 0;
    });

    const averageSuccessRate = successRates.reduce((sum, rate) => sum + rate, 0) / successRates.length;

    // Analyze best conditions for this target
    const bestConditions = this.findBestConditionsForTarget(targetSessions);

    // Generate recommendations
    const recommendations = this.generateTargetRecommendations(targetSessions, averageSuccessRate);

    return {
      sessions: targetSessions,
      averageSuccessRate,
      bestConditions,
      recommendations
    };
  }

  private generateInsights(): void {
    this.insights = [];

    // Generate various types of insights
    this.insights.push(...this.analyzeSuccessPatterns());
    this.insights.push(...this.analyzeEquipmentIssues());
    this.insights.push(...this.analyzeWeatherPatterns());
    this.insights.push(...this.analyzeTechnicalTrends());
    this.insights.push(...this.generateImprovementSuggestions());
  }

  private analyzeSuccessPatterns(): SessionInsight[] {
    const insights: SessionInsight[] = [];

    if (this.sessions.length < 3) return insights;

    // Analyze time-based patterns
    const timeAnalysis = this.analyzeTimePatterns();
    if (timeAnalysis.insight) {
      insights.push(timeAnalysis.insight);
    }

    // Analyze target type patterns
    const targetAnalysis = this.analyzeTargetTypePatterns();
    if (targetAnalysis.insight) {
      insights.push(targetAnalysis.insight);
    }

    return insights;
  }

  private analyzeEquipmentIssues(): SessionInsight[] {
    const insights: SessionInsight[] = [];

    // Analyze HFR trends
    const hfrTrend = this.analyzeHFRTrend();
    if (hfrTrend.isWorsening) {
      insights.push({
        type: 'warning',
        category: 'equipment',
        title: 'Focus Quality Declining',
        description: 'Your average HFR has been increasing over recent sessions, indicating potential focus issues.',
        impact: 'high',
        actionable: true,
        recommendations: [
          'Check focuser backlash and tighten connections',
          'Verify mirror lock-up is working properly',
          'Consider temperature compensation',
          'Check for optical misalignment'
        ],
        confidence: hfrTrend.confidence
      });
    }

    return insights;
  }

  private analyzeWeatherPatterns(): SessionInsight[] {
    const insights: SessionInsight[] = [];

    const weatherAnalysis = this.findOptimalWeatherConditions();
    if (weatherAnalysis.hasPattern) {
      insights.push({
        type: 'tip',
        category: 'conditions',
        title: 'Optimal Weather Conditions Identified',
        description: `Your best sessions occur when ${weatherAnalysis.description}`,
        impact: 'medium',
        actionable: true,
        recommendations: weatherAnalysis.recommendations,
        confidence: weatherAnalysis.confidence
      });
    }

    return insights;
  }

  private analyzeTechnicalTrends(): SessionInsight[] {
    const insights: SessionInsight[] = [];

    // Analyze exposure time effectiveness
    const exposureAnalysis = this.analyzeExposureTimes();
    if (exposureAnalysis.hasInsight) {
      insights.push(exposureAnalysis.insight);
    }

    return insights;
  }

  private generateImprovementSuggestions(): SessionInsight[] {
    const insights: SessionInsight[] = [];

    // Suggest improvements based on patterns
    const metrics = this.getAnalyticsMetrics();
    
    if (metrics.successRate < 70) {
      insights.push({
        type: 'improvement',
        category: 'technique',
        title: 'Session Success Rate Below Average',
        description: `Your current success rate is ${Math.round(metrics.successRate)}%. Here are some ways to improve.`,
        impact: 'high',
        actionable: true,
        recommendations: [
          'Focus on easier targets to build confidence',
          'Improve polar alignment accuracy',
          'Use longer exposure times for better SNR',
          'Consider upgrading guiding system'
        ],
        confidence: 85
      });
    }

    return insights;
  }

  // Helper methods for analysis
  private getEmptyMetrics(): AnalyticsMetrics {
    return {
      totalSessions: 0,
      totalImagingTime: 0,
      averageSessionDuration: 0,
      successRate: 0,
      mostSuccessfulTargetTypes: [],
      bestImagingConditions: {
        temperature: { min: 0, max: 0 },
        humidity: { min: 0, max: 0 },
        seeing: { min: 0, max: 0 },
        moonPhase: { min: 0, max: 0 }
      },
      equipmentPerformance: {},
      improvementTrends: {
        hfr: 'stable',
        snr: 'stable',
        successRate: 'stable'
      }
    };
  }

  private analyzeBestConditions() {
    const successfulSessions = this.sessions.filter(s => s.successRate >= 80);
    
    if (successfulSessions.length === 0) {
      return {
        temperature: { min: 0, max: 30 },
        humidity: { min: 0, max: 80 },
        seeing: { min: 1, max: 3 },
        moonPhase: { min: 0, max: 0.3 }
      };
    }

    const temps = successfulSessions.map(s => s.weather.temperature);
    const humidity = successfulSessions.map(s => s.weather.humidity);
    const seeing = successfulSessions.map(s => s.astronomical.seeing);
    const moonPhase = successfulSessions.map(s => s.astronomical.moonPhase);

    return {
      temperature: { min: Math.min(...temps), max: Math.max(...temps) },
      humidity: { min: Math.min(...humidity), max: Math.max(...humidity) },
      seeing: { min: Math.min(...seeing), max: Math.max(...seeing) },
      moonPhase: { min: Math.min(...moonPhase), max: Math.max(...moonPhase) }
    };
  }

  private analyzeEquipmentPerformance() {
    const performance: { [key: string]: any } = {};
    
    // Group sessions by equipment
    const equipmentGroups = new Map<string, ImagingSession[]>();
    this.sessions.forEach(session => {
      const key = `${session.equipment.camera?.model}-${session.equipment.telescope?.model}`;
      const sessions = equipmentGroups.get(key) || [];
      sessions.push(session);
      equipmentGroups.set(key, sessions);
    });

    equipmentGroups.forEach((sessions, equipmentKey) => {
      const successRate = sessions.reduce((sum, s) => sum + s.successRate, 0) / sessions.length;
      
      const allMetrics = sessions.flatMap(s => 
        s.targets.flatMap(t => t.imageMetrics)
      );
      
      const avgHFR = allMetrics.reduce((sum, m) => sum + m.hfr, 0) / allMetrics.length || 0;
      const avgSNR = allMetrics.reduce((sum, m) => sum + m.snr, 0) / allMetrics.length || 0;
      
      performance[equipmentKey] = {
        successRate,
        averageHFR: avgHFR,
        averageSNR: avgSNR,
        reliability: successRate / 100
      };
    });

    return performance;
  }

  private analyzeImprovementTrends() {
    if (this.sessions.length < 5) {
      return {
        hfr: 'stable' as const,
        snr: 'stable' as const,
        successRate: 'stable' as const
      };
    }

    const recentSessions = this.sessions.slice(-5);
    const olderSessions = this.sessions.slice(-10, -5);

    const recentHFR = this.getAverageHFR(recentSessions);
    const olderHFR = this.getAverageHFR(olderSessions);
    
    const recentSNR = this.getAverageSNR(recentSessions);
    const olderSNR = this.getAverageSNR(olderSessions);
    
    const recentSuccess = recentSessions.reduce((sum, s) => sum + s.successRate, 0) / recentSessions.length;
    const olderSuccess = olderSessions.reduce((sum, s) => sum + s.successRate, 0) / olderSessions.length;

    return {
      hfr: this.getTrend(recentHFR, olderHFR, true), // Lower is better for HFR
      snr: this.getTrend(recentSNR, olderSNR, false), // Higher is better for SNR
      successRate: this.getTrend(recentSuccess, olderSuccess, false)
    };
  }

  private getTrend(recent: number, older: number, lowerIsBetter: boolean): 'improving' | 'stable' | 'declining' {
    const threshold = 0.05; // 5% threshold
    const improvement = lowerIsBetter ? older - recent : recent - older;
    const percentChange = Math.abs(improvement) / older;

    if (percentChange < threshold) return 'stable';
    return improvement > 0 ? 'improving' : 'declining';
  }

  private getAverageHFR(sessions: ImagingSession[]): number {
    const allMetrics = sessions.flatMap(s => s.targets.flatMap(t => t.imageMetrics));
    return allMetrics.reduce((sum, m) => sum + m.hfr, 0) / allMetrics.length || 0;
  }

  private getAverageSNR(sessions: ImagingSession[]): number {
    const allMetrics = sessions.flatMap(s => s.targets.flatMap(t => t.imageMetrics));
    return allMetrics.reduce((sum, m) => sum + m.snr, 0) / allMetrics.length || 0;
  }

  // Placeholder methods for complex analysis
  private analyzeTimePatterns(): { insight?: SessionInsight } {
    return {};
  }

  private analyzeTargetTypePatterns(): { insight?: SessionInsight } {
    return {};
  }

  private analyzeHFRTrend(): { isWorsening: boolean; confidence: number } {
    return { isWorsening: false, confidence: 0 };
  }

  private findOptimalWeatherConditions(): { hasPattern: boolean; description: string; recommendations: string[]; confidence: number } {
    return { hasPattern: false, description: '', recommendations: [], confidence: 0 };
  }

  private analyzeExposureTimes(): { hasInsight: boolean; insight: SessionInsight } {
    return { hasInsight: false, insight: {} as SessionInsight };
  }

  private findBestConditionsForTarget(sessions: ImagingSession[]): any {
    return null;
  }

  private generateTargetRecommendations(sessions: ImagingSession[], successRate: number): string[] {
    return [];
  }
}
