import { Target } from '../target-planning/target-database';
import { EquipmentProfile } from '../stores/equipment-store';
import { WeatherConditions, AstronomicalConditions } from '../weather/weather-service';

export interface OptimizationParameters {
  maxImagingTime: number; // minutes
  minTargetAltitude: number; // degrees
  maxAirmass: number;
  moonAvoidanceAngle: number; // degrees
  prioritizeNewTargets: boolean;
  balanceTargetTypes: boolean;
  considerWeather: boolean;
}

export interface OptimizedSession {
  targets: OptimizedTarget[];
  totalDuration: number; // minutes
  estimatedCompletion: number; // percentage
  qualityScore: number; // 0-100
  recommendations: string[];
  warnings: string[];
}

export interface OptimizedTarget {
  target: Target;
  startTime: Date;
  endTime: Date;
  duration: number; // minutes
  altitude: number; // degrees at start
  airmass: number;
  priority: number; // 1-10
  filters: string[];
  exposureSettings: {
    exposureTime: number; // seconds
    frameCount: number;
    totalTime: number; // minutes
  };
  qualityPrediction: number; // 0-100
}

export class TargetOptimizer {
  private location: { latitude: number; longitude: number };
  private timezone: string;

  constructor(location: { latitude: number; longitude: number; timezone: string }) {
    this.location = { latitude: location.latitude, longitude: location.longitude };
    this.timezone = location.timezone;
  }

  optimizeSession(
    targets: Target[],
    equipment: EquipmentProfile,
    sessionStart: Date,
    sessionEnd: Date,
    parameters: OptimizationParameters,
    weather?: WeatherConditions,
    astronomical?: AstronomicalConditions
  ): OptimizedSession {
    const sessionDuration = (sessionEnd.getTime() - sessionStart.getTime()) / (1000 * 60);
    const optimizedTargets: OptimizedTarget[] = [];
    const recommendations: string[] = [];
    const warnings: string[] = [];

    // Filter targets by visibility and constraints
    const viableTargets = this.filterViableTargets(
      targets,
      sessionStart,
      sessionEnd,
      parameters
    );

    if (viableTargets.length === 0) {
      warnings.push('No targets meet the current constraints');
      return {
        targets: [],
        totalDuration: 0,
        estimatedCompletion: 0,
        qualityScore: 0,
        recommendations: ['Consider relaxing altitude or airmass constraints'],
        warnings
      };
    }

    // Calculate optimal imaging windows for each target
    const targetWindows = viableTargets.map(target => 
      this.calculateImagingWindow(target, sessionStart, sessionEnd, parameters)
    );

    // Sort by priority and quality
    const sortedWindows = targetWindows
      .filter(window => window.duration > 30) // Minimum 30 minutes
      .sort((a, b) => b.qualityPrediction - a.qualityPrediction);

    // Allocate time to targets
    let remainingTime = sessionDuration;
    let currentTime = new Date(sessionStart);

    for (const window of sortedWindows) {
      if (remainingTime < 30) break; // Not enough time for meaningful imaging

      const allocatedTime = Math.min(
        window.duration,
        remainingTime,
        parameters.maxImagingTime
      );

      if (allocatedTime >= 30) {
        const optimizedTarget: OptimizedTarget = {
          ...window,
          startTime: new Date(currentTime),
          endTime: new Date(currentTime.getTime() + allocatedTime * 60 * 1000),
          duration: allocatedTime,
          exposureSettings: this.calculateExposureSettings(
            window.target,
            equipment,
            allocatedTime
          )
        };

        optimizedTargets.push(optimizedTarget);
        remainingTime -= allocatedTime;
        currentTime = new Date(currentTime.getTime() + allocatedTime * 60 * 1000);
      }
    }

    // Generate recommendations
    if (optimizedTargets.length === 0) {
      recommendations.push('Consider extending session duration or relaxing constraints');
    } else {
      recommendations.push(...this.generateRecommendations(optimizedTargets, parameters));
    }

    // Calculate quality metrics
    const totalDuration = optimizedTargets.reduce((sum, t) => sum + t.duration, 0);
    const avgQuality = optimizedTargets.reduce((sum, t) => sum + t.qualityPrediction, 0) / optimizedTargets.length || 0;
    const completion = (totalDuration / sessionDuration) * 100;

    return {
      targets: optimizedTargets,
      totalDuration,
      estimatedCompletion: completion,
      qualityScore: avgQuality,
      recommendations,
      warnings
    };
  }

  private filterViableTargets(
    targets: Target[],
    sessionStart: Date,
    sessionEnd: Date,
    parameters: OptimizationParameters
  ): Target[] {
    return targets.filter(target => {
      // Check if target is above minimum altitude during session
      const midSession = new Date((sessionStart.getTime() + sessionEnd.getTime()) / 2);
      const altitude = this.calculateAltitude(target, midSession);
      const airmass = this.calculateAirmass(altitude);

      return altitude >= parameters.minTargetAltitude && 
             airmass <= parameters.maxAirmass;
    });
  }

  private calculateImagingWindow(
    target: Target,
    sessionStart: Date,
    sessionEnd: Date,
    parameters: OptimizationParameters
  ): OptimizedTarget {
    const midSession = new Date((sessionStart.getTime() + sessionEnd.getTime()) / 2);
    const altitude = this.calculateAltitude(target, midSession);
    const airmass = this.calculateAirmass(altitude);
    
    // Calculate quality prediction based on various factors
    let qualityPrediction = 70; // Base quality

    // Altitude bonus
    if (altitude > 60) qualityPrediction += 15;
    else if (altitude > 45) qualityPrediction += 10;
    else if (altitude < 30) qualityPrediction -= 10;

    // Airmass penalty
    if (airmass > 2) qualityPrediction -= 15;
    else if (airmass > 1.5) qualityPrediction -= 5;

    // Target brightness
    if (target.magnitude < 8) qualityPrediction += 10;
    else if (target.magnitude > 12) qualityPrediction -= 10;

    const sessionDuration = (sessionEnd.getTime() - sessionStart.getTime()) / (1000 * 60);
    const estimatedDuration = Math.min(sessionDuration, this.estimateOptimalDuration(target));

    return {
      target,
      startTime: sessionStart,
      endTime: sessionEnd,
      duration: estimatedDuration,
      altitude,
      airmass,
      priority: this.calculatePriority(target, altitude, airmass),
      filters: this.recommendFilters(target),
      exposureSettings: {
        exposureTime: 300, // Will be calculated properly
        frameCount: 0,
        totalTime: 0
      },
      qualityPrediction: Math.max(0, Math.min(100, qualityPrediction))
    };
  }

  private calculateExposureSettings(
    target: Target,
    equipment: EquipmentProfile,
    duration: number
  ) {
    // Base exposure time based on target type and equipment
    let exposureTime = 300; // 5 minutes default

    if (target.type === 'galaxy' && target.magnitude > 10) {
      exposureTime = 600; // 10 minutes for faint galaxies
    } else if (target.type === 'planetary nebula') {
      exposureTime = 180; // 3 minutes for planetary nebulae
    } else if (target.type === 'emission nebula') {
      exposureTime = 900; // 15 minutes for emission nebulae
    }

    // Adjust for equipment
    if (equipment.telescope?.aperture && equipment.telescope.aperture < 100) {
      exposureTime *= 1.5; // Longer exposures for smaller telescopes
    }

    const frameCount = Math.floor((duration * 60) / exposureTime);
    const totalTime = (frameCount * exposureTime) / 60;

    return {
      exposureTime,
      frameCount,
      totalTime
    };
  }

  private generateRecommendations(
    targets: OptimizedTarget[],
    parameters: OptimizationParameters
  ): string[] {
    const recommendations: string[] = [];

    if (targets.length === 1) {
      recommendations.push('Consider adding more targets for variety');
    }

    const avgAirmass = targets.reduce((sum, t) => sum + t.airmass, 0) / targets.length;
    if (avgAirmass > 1.8) {
      recommendations.push('High airmass detected - consider waiting for better positioning');
    }

    const totalDuration = targets.reduce((sum, t) => sum + t.duration, 0);
    if (totalDuration < parameters.maxImagingTime * 0.8) {
      recommendations.push('Session could be extended for better results');
    }

    return recommendations;
  }

  // Astronomical calculation helpers
  private calculateAltitude(target: Target, time: Date): number {
    // Simplified altitude calculation
    // In a real implementation, this would use proper astronomical calculations
    const hourAngle = this.calculateHourAngle(target, time);
    const dec = target.coordinates.dec;
    const lat = this.location.latitude;

    const altitude = Math.asin(
      Math.sin(dec * Math.PI / 180) * Math.sin(lat * Math.PI / 180) +
      Math.cos(dec * Math.PI / 180) * Math.cos(lat * Math.PI / 180) * Math.cos(hourAngle * Math.PI / 180)
    ) * 180 / Math.PI;

    return Math.max(0, altitude);
  }

  private calculateAirmass(altitude: number): number {
    if (altitude <= 0) return 999;
    return 1 / Math.sin(altitude * Math.PI / 180);
  }

  private calculateHourAngle(target: Target, time: Date): number {
    // Simplified hour angle calculation
    const lst = this.calculateLocalSiderealTime(time);
    return lst - target.coordinates.ra;
  }

  private calculateLocalSiderealTime(time: Date): number {
    // Simplified LST calculation
    const jd = this.dateToJulianDay(time);
    const t = (jd - 2451545.0) / 36525;
    const gmst = 280.46061837 + 360.98564736629 * (jd - 2451545) + 0.000387933 * t * t;
    return (gmst + this.location.longitude) % 360;
  }

  private dateToJulianDay(date: Date): number {
    return (date.getTime() / 86400000) + 2440587.5;
  }

  private calculatePriority(target: Target, altitude: number, airmass: number): number {
    let priority = 5; // Base priority

    if (altitude > 60) priority += 2;
    if (airmass < 1.3) priority += 2;
    if (target.magnitude < 8) priority += 1;

    return Math.max(1, Math.min(10, priority));
  }

  private recommendFilters(target: Target): string[] {
    if (target.type === 'emission nebula') return ['Ha', 'OIII', 'SII'];
    if (target.type === 'galaxy') return ['L', 'R', 'G', 'B'];
    if (target.type === 'planetary nebula') return ['OIII', 'Ha'];
    return ['L', 'R', 'G', 'B'];
  }

  private estimateOptimalDuration(target: Target): number {
    let duration = 120; // 2 hours base

    if (target.magnitude > 10) duration += 60;
    if (target.magnitude > 12) duration += 120;
    if (target.size < 5) duration += 60;

    return duration;
  }
}
