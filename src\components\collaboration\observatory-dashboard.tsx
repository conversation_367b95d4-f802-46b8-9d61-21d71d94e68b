"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Observatory, 
  ObservatoryMember, 
  SessionBooking, 
  CollaborationEvent,
  ObservatoryManager 
} from '@/lib/collaboration/observatory-manager';
import { cn } from '@/lib/utils';
import {
  Users,
  Calendar,
  Activity,
  Settings,
  Plus,
  Clock,
  MapPin,
  Star,
  Telescope,
  Camera,
  MessageCircle,
  Bell,
  Shield,
  TrendingUp,
  Eye,
  UserPlus,
  UserMinus,
  Play,
  Pause,
  CheckCircle,
  AlertTriangle,
  Info,
  Crown,
  Zap
} from 'lucide-react';

interface ObservatoryDashboardProps {
  observatory: Observatory;
  currentUserId: string;
  observatoryManager: ObservatoryManager;
  className?: string;
}

export function ObservatoryDashboard({ 
  observatory, 
  currentUserId, 
  observatoryManager,
  className 
}: ObservatoryDashboardProps) {
  const [selectedTab, setSelectedTab] = useState('overview');
  const [recentBookings, setRecentBookings] = useState<SessionBooking[]>([]);
  const [recentEvents, setRecentEvents] = useState<CollaborationEvent[]>([]);
  const [onlineMembers, setOnlineMembers] = useState<Set<string>>(new Set());

  const currentMember = observatory.members.find(m => m.userId === currentUserId);
  const isOwner = observatory.owner === currentUserId;
  const canManageMembers = isOwner || observatoryManager.hasPermission(observatory.id, currentUserId, 'manage_members');
  const canBookSessions = observatoryManager.hasPermission(observatory.id, currentUserId, 'book_sessions');

  useEffect(() => {
    // Load recent bookings
    const bookings = observatoryManager.getObservatoryBookings(observatory.id);
    setRecentBookings(bookings.slice(0, 10));

    // Load recent events
    const events = observatoryManager.getObservatoryEvents(observatory.id, 20);
    setRecentEvents(events);

    // Simulate online status (in real app, this would come from WebSocket)
    const simulateOnlineStatus = () => {
      const online = new Set<string>();
      observatory.members.forEach(member => {
        if (Math.random() > 0.6) { // 40% chance of being online
          online.add(member.userId);
        }
      });
      setOnlineMembers(online);
    };

    simulateOnlineStatus();
    const interval = setInterval(simulateOnlineStatus, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [observatory.id, observatoryManager]);

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner': return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'admin': return <Shield className="h-4 w-4 text-blue-500" />;
      case 'operator': return <Settings className="h-4 w-4 text-green-500" />;
      case 'observer': return <Eye className="h-4 w-4 text-purple-500" />;
      default: return <Users className="h-4 w-4 text-gray-500" />;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'owner': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'admin': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'operator': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'observer': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getBookingStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-200';
      case 'scheduled': return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-200';
      case 'completed': return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-200';
      case 'cancelled': return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-200';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'member_joined': return <UserPlus className="h-4 w-4 text-green-500" />;
      case 'member_left': return <UserMinus className="h-4 w-4 text-red-500" />;
      case 'session_started': return <Play className="h-4 w-4 text-blue-500" />;
      case 'session_ended': return <Pause className="h-4 w-4 text-gray-500" />;
      case 'equipment_issue': return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'message': return <MessageCircle className="h-4 w-4 text-purple-500" />;
      default: return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  const activeBookings = recentBookings.filter(b => b.status === 'active');
  const upcomingBookings = recentBookings.filter(b => 
    b.status === 'scheduled' && b.startTime > new Date()
  ).slice(0, 5);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-primary/10 rounded-lg">
            <Telescope className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">{observatory.name}</h1>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                {observatory.location.name}
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                {observatory.members.length} members
              </div>
              <div className="flex items-center gap-1">
                <Activity className="h-4 w-4" />
                {onlineMembers.size} online
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {canBookSessions && (
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Book Session
            </Button>
          )}
          {canManageMembers && (
            <Button variant="outline">
              <UserPlus className="h-4 w-4 mr-2" />
              Invite Members
            </Button>
          )}
        </div>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="equipment">Equipment</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Active Sessions */}
          {activeBookings.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Play className="h-5 w-5 text-green-500" />
                  Active Sessions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {activeBookings.map(booking => (
                  <div key={booking.id} className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div>
                      <div className="font-medium">{booking.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {booking.username} • Started {formatTimeAgo(booking.actualStartTime || booking.startTime)}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className="bg-green-100 text-green-800">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse" />
                        Live
                      </Badge>
                      <Button size="sm" variant="outline">
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Sessions</p>
                    <p className="text-2xl font-bold">{observatory.statistics.totalSessions}</p>
                  </div>
                  <Calendar className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Imaging Time</p>
                    <p className="text-2xl font-bold">{Math.round(observatory.statistics.totalImagingTime)}h</p>
                  </div>
                  <Clock className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Equipment Usage</p>
                    <p className="text-2xl font-bold">{Math.round(observatory.statistics.equipmentUtilization)}%</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Active Members</p>
                    <p className="text-2xl font-bold">{observatory.statistics.activeMembers}</p>
                  </div>
                  <Users className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Upcoming Sessions */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Upcoming Sessions
                </CardTitle>
              </CardHeader>
              <CardContent>
                {upcomingBookings.length > 0 ? (
                  <div className="space-y-3">
                    {upcomingBookings.map(booking => (
                      <div key={booking.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{booking.title}</div>
                          <div className="text-sm text-muted-foreground">
                            {booking.username} • {booking.startTime.toLocaleDateString()} at {booking.startTime.toLocaleTimeString()}
                          </div>
                        </div>
                        <Badge className={getBookingStatusColor(booking.status)}>
                          {booking.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No upcoming sessions</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[300px]">
                  {recentEvents.length > 0 ? (
                    <div className="space-y-3">
                      {recentEvents.map(event => {
                        const Icon = getEventIcon(event.type);
                        return (
                          <div key={event.id} className="flex items-start gap-3">
                            <div className="p-1 bg-muted rounded">
                              {Icon}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm">
                                <span className="font-medium">{event.username}</span>{' '}
                                {event.type.replace('_', ' ')}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {formatTimeAgo(event.timestamp)}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No recent activity</p>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Session Schedule</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-muted-foreground">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Calendar view will be implemented with a calendar library</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="members" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Observatory Members ({observatory.members.length})
                </CardTitle>
                {canManageMembers && (
                  <Button size="sm">
                    <UserPlus className="h-4 w-4 mr-2" />
                    Invite Member
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {observatory.members.map(member => (
                  <div key={member.userId} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <Avatar>
                          <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${member.username}`} />
                          <AvatarFallback>{member.username.slice(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        {onlineMembers.has(member.userId) && (
                          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
                        )}
                      </div>
                      <div>
                        <div className="font-medium flex items-center gap-2">
                          {member.username}
                          {getRoleIcon(member.role)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Last active {formatTimeAgo(member.lastActive)}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getRoleBadgeColor(member.role)}>
                        {member.role}
                      </Badge>
                      {member.status === 'active' ? (
                        <Badge variant="outline" className="text-green-600">
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-gray-600">
                          {member.status}
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="equipment" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {observatory.equipment.map((equipment, index) => (
              <Card key={index}>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2">
                    <Camera className="h-5 w-5" />
                    <CardTitle className="text-lg">{equipment.name}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    {equipment.telescope && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">Telescope:</span> {equipment.telescope.model}
                      </div>
                    )}
                    {equipment.camera && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">Camera:</span> {equipment.camera.model}
                      </div>
                    )}
                    {equipment.mount && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">Mount:</span> {equipment.mount.model}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-green-600">
                      Available
                    </Badge>
                    <Button size="sm" variant="outline">
                      Book
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Observatory Activity Feed
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px]">
                {recentEvents.length > 0 ? (
                  <div className="space-y-4">
                    {recentEvents.map(event => {
                      const Icon = getEventIcon(event.type);
                      return (
                        <div key={event.id} className="flex items-start gap-3 p-3 border rounded-lg">
                          <div className="p-2 bg-muted rounded">
                            {Icon}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <p className="font-medium">
                                {event.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </p>
                              <span className="text-xs text-muted-foreground">
                                {formatTimeAgo(event.timestamp)}
                              </span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              <span className="font-medium">{event.username}</span>{' '}
                              {this.getEventDescription(event)}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-12 text-muted-foreground">
                    <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No activity to show</p>
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );

  // Helper method for event descriptions
  function getEventDescription(event: CollaborationEvent): string {
    switch (event.type) {
      case 'member_joined': return 'joined the observatory';
      case 'member_left': return 'left the observatory';
      case 'session_started': return 'started an imaging session';
      case 'session_ended': return 'completed an imaging session';
      case 'equipment_issue': return 'reported an equipment issue';
      case 'message': return 'sent a message';
      default: return 'performed an action';
    }
  }
}
